<script>
  import { enhance } from "$app/forms";
  import { createEventDispatcher } from "svelte";



  // Props for the component
  export let title = "APK Repacker";
  export let templatePath = "";
  export let defaultConfig = "";
  export let configFilename = "vpn.conf";
  export let csrfToken = "";
  export let id = "apk-repacker-" + Math.random().toString(36).substring(2, 9); // Generate a unique ID for this component instance
  export let submissionDelay = 0; // Optional delay in ms before submission (to prevent race conditions)

  // Event dispatcher
  const dispatch = createEventDispatcher();

  // Local state
  let configText = defaultConfig;
  let assetFilename = configFilename;
  let processing = false;
  let downloadUrl = "";
  let downloadFilename = "";
  let clientStartTime = 0; // Track when the client started the repacking process

  

  // Direct form submission as a fallback
  async function submitForm() {
    if (!templatePath) {
      alert("Template path is not set");
      return;
    }

    if (!configText) {
      alert("Please enter configuration");
      return;
    }

    processing = true;
    // Start client-side timer
    clientStartTime = Date.now();

    // Generate a unique submission ID for this specific direct submission
    const submissionId = `direct-${id}-${Date.now()}`;

    try {
      // Create a FormData object
      const formData = new FormData();
      formData.append("csrfToken", csrfToken);

      // Add the template path to the form data
      formData.append("templatePath", templatePath);

      // Add the configuration to the form data
      formData.append("config", configText);

      // Add the asset filename to the form data
      formData.append("assetFilename", assetFilename);

      // Add the component ID to create a unique job ID
      formData.append("componentId", id);

      // Add the submission ID for tracking
      formData.append("submissionId", submissionId);

      // Apply submission delay if specified (to prevent race conditions)
      if (submissionDelay > 0) {
        await new Promise(resolve => setTimeout(resolve, submissionDelay));
      }

      // Submit the form data with timeout and error handling
      const controller = new AbortController();


      await fetch("?/repackApk", {
          method: "POST",
          body: formData,
          signal: controller.signal
        });
    } catch (err) {
      console.error(`[${id}] Error submitting form for ${submissionId}:`, err);

      // Handle specific error types
      let errorMessage = "An unknown error occurred";

      if (err instanceof Error) {
        errorMessage = err.message || "Error submitting form";
        console.error(`[${id}] Error details for ${submissionId}:`, {
          name: err.name,
          message: err.message,
          stack: err.stack
        });

        if (err.name === "AbortError") {
          console.error(`[${id}] Request timeout for ${submissionId}`);
          dispatch("result", {
            success: false,
            error: "Request timeout",
            details: "The request took too long and was aborted. Please try again or check your connection.",
            submissionId
          });
        } else {
          console.error(`[${id}] General error for ${submissionId}: ${errorMessage}`);
          dispatch("result", {
            success: false,
            error: "Error submitting form",
            details: errorMessage,
            submissionId
          });
        }
      } else {
        console.error(`[${id}] Unknown error type for ${submissionId}`);
        dispatch("result", {
          success: false,
          error: "Error submitting form",
          details: "An unknown error occurred",
          submissionId
        });
      }
    } finally {
      processing = false;
    }
  }

  // Handle form submission with enhance
  function handleSubmit() {
    processing = true;
    // Start client-side timer
    clientStartTime = Date.now();

    // Generate a unique submission ID for this specific submission
    const submissionId = `${id}-${Date.now()}`;

    /**
     * @param {{ result: { type: string, data?: any }, update: Function, formData: FormData }} param0
     */
    return async ({ result, update, formData }) => {
      try {
        // Make sure the template path is included in the form data
        formData.set("templatePath", templatePath);

        // Add the component ID to create a unique job ID
        formData.set("componentId", id);

        // Add the submission ID for tracking
        formData.set("submissionId", submissionId);

        // Apply submission delay if specified (to prevent race conditions)
        if (submissionDelay > 0) {
          await new Promise(resolve => setTimeout(resolve, submissionDelay));
        }

        // Update the form result
        await update();
      } catch (error) {
        console.error(`[${id}] Error in form submission ${submissionId}:`, error);
        // Use direct submission as fallback
        submitForm();
        return;
      } finally {
        processing = false;
      }

      // Reset form state for subsequent submissions
      if (result.type === "success" && result.data) {
        let formResult = result.data;

        // Check if the result data is a string (needs to be parsed)
        if (typeof formResult === 'string') {
          try {

            // Try to parse the data as JSON
            const parsedData = JSON.parse(formResult);

            // Check if the parsed data is an array with a specific format
            if (Array.isArray(parsedData) && parsedData.length > 0) {
              try {
                // Extract the first element as the index map
                const firstElement = parsedData[0];

                if (firstElement && typeof firstElement === 'object') {
                  // Extract the values (all elements after the first)
                  const values = parsedData.slice(1);

                  // Create a new object to hold the properly mapped properties
                  let properObject = Object.create(null);

                  // For each key in the first element (index map)
                  Object.keys(firstElement).forEach(key => {
                    // Get the index value (safely)
                    let indexValue = firstElement[key];

                    // Convert to number and validate
                    let index = parseInt(String(indexValue), 10);

                    // If it's a valid index, map the value
                    if (!isNaN(index) && index >= 1 && index <= values.length) {
                      // Set the property
                      properObject[key] = values[index - 1];
                    }
                  });

                  formResult = properObject;
                } else {
                  // If the first element isn't an object, just use the parsed data
                  formResult = parsedData;
                }
              } catch (mappingError) {
                console.error(`[${id}] Error mapping enhanced array data:`, mappingError);
                // Fall back to the original parsed data
                formResult = parsedData;
              }
            } else {
              // If it's not an array, just use the parsed data
              formResult = parsedData;
            }
          } catch (parseError) {
            console.error(`[${id}] Error parsing enhanced result data for ${submissionId}:`, parseError);
            // Keep the original result if parsing fails
          }
        }

        // Add submission ID to the result for tracking
        formResult.submissionId = submissionId;

        // Dispatch the result event
        dispatch("result", formResult);

        // Create download link for the repacked APK
        if (formResult.success && formResult.jobId && formResult.apkName) {
          downloadUrl = `/admin/apk/download/${formResult.jobId}/${formResult.apkName}`;
          downloadFilename = formResult.apkName;


          // Calculate total client-side time (includes network transfer)
          const clientTotalTime = ((Date.now() - clientStartTime) / 1000).toFixed(2);

          // Use client-side timing for the user-facing message
          formResult.message = `APK repacked successfully in ${clientTotalTime} seconds`;

        } else {
          // Clear download link if there was an error
          downloadUrl = "";
          downloadFilename = "";

          if (!formResult.success) {
            const errorMsg = formResult.error || 'Unknown error';
            const errorDetails = formResult.details || 'No details available';
            console.error(`[${id}] APK repacking failed for enhanced submission ${submissionId}:`, errorMsg, errorDetails);
            console.error(`[${id}] Full error result for enhanced submission ${submissionId}:`, formResult);
          } else {
            console.warn(`[${id}] Enhanced submission ${submissionId} was successful but missing jobId or apkName`);
          }
        }
      } else {
        // Handle non-success result
        console.error(`[${id}] Enhanced form submission ${submissionId} returned non-success result:`, result);
        downloadUrl = "";
        downloadFilename = "";
      }
    };
  }

  // Reset the component
  function reset() {
    downloadUrl = "";
    downloadFilename = "";
    configText = defaultConfig;
    assetFilename = configFilename;
  }

  // Check if the component is ready for submission
  export function isReadyForSubmission() {
    return templatePath && configText && !processing;
  }

  // Expose the submitForm function for external triggering
  export function triggerSubmit() {
    if (isReadyForSubmission()) {
      submitForm();
      return true;
    }
    return false;
  }

  // Expose the download URL and filename
  export function getDownloadInfo() {
    // Extract jobId from downloadUrl if available
    let jobId = null;
    let url = downloadUrl;

    if (downloadUrl) {
      const match = downloadUrl.match(/\/admin\/apk\/download\/([^/]+)\/([^/]+)/);
      if (match && match.length >= 3) {
        const jobIdPart = match[1];
        const filename = match[2];

        // Extract the timestamp part if jobId contains a dash
        if (jobIdPart.includes('-')) {
          const timestampMatch = jobIdPart.match(/^(\d+)/);
          if (timestampMatch) {
            jobId = timestampMatch[1];
            // Also update the URL to use the correct format
            url = `/admin/apk/download/${jobId}/${filename}`;
          } else {
            jobId = jobIdPart;
          }
        } else {
          jobId = jobIdPart;
        }
      }
    }

    return {
      url: url,
      filename: downloadFilename,
      isReady: !!downloadUrl,
      jobId: jobId
    };
  }

  // Expose the title for logging purposes
  export function getTitle() {
    return title;
  }
</script>

<!-- APK Repacker Component -->
<div class="apk-repacker-component">
  <div class="component-header">
    <div class="component-title">{title}</div>
  </div>

  <form
    method="POST"
    action="?/repackApk"
    enctype="multipart/form-data"
    use:enhance={handleSubmit}
  >
    <!-- CSRF Token -->
    {#if csrfToken}
      <input type="hidden" name="csrfToken" value={csrfToken} />
    {/if}

    <!-- Template Path (hidden) -->
    <input type="hidden" name="templatePath" value={templatePath} />

    <!-- Configuration Text Area -->
    <div class="form-group">
      <label for="{id}-configTextarea" class="form-label">Configuration</label>
      <textarea
        id="{id}-configTextarea"
        name="config"
        class="config-textarea"
        bind:value={configText}
        rows="8"
      ></textarea>

      <!-- Asset Filename Field -->
      <div class="asset-filename-container">
        <label for="{id}-assetFilename" class="form-label">Asset Filename</label>
        <input
          type="text"
          id="{id}-assetFilename"
          name="assetFilename"
          class="config-textarea asset-filename-input"
          bind:value={assetFilename}
          placeholder={configFilename}
        />
        <div class="asset-filename-help">
          Name of the configuration file in the APK assets folder
        </div>
      </div>
    </div>

    <!-- Submit Button -->
    <div class="form-actions">
      {#if downloadUrl}
        <!-- Show download button when APK is ready -->
        <div class="download-actions">
          <button
            type="button"
            class="button reset-button"
            on:click={reset}
          >
            Repack Again
          </button>
          <button
            type="button"
            class="button download-button"
            on:click={async () => {
              // Ensure the URL is correct before downloading
              let finalUrl = downloadUrl;

              if (!downloadUrl.startsWith('/admin/apk/download/')) {
                // Try to extract jobId from the URL
                const match = downloadUrl.match(/\/([^/]+)\/([^/]+)$/);
                if (match && match.length >= 3) {
                  const jobId = match[1];
                  const filename = match[2];
                  finalUrl = `/admin/apk/download/${jobId}/${filename}`;
                } else {
                  console.error(`[${id}] Cannot correct download URL: ${downloadUrl}`);
                  alert('Download link is invalid. Please try repacking again.');
                  return;
                }
              } else {
                // URL starts with the correct prefix but might still have the wrong format
                const match = downloadUrl.match(/\/admin\/apk\/download\/([^/]+)\/([^/]+)/);
                if (match && match.length >= 3) {
                  const jobIdPart = match[1];
                  const filename = match[2];

                  // Check if jobId contains a dash (indicating it's the full component ID)
                  if (jobIdPart.includes('-')) {
                    // Extract just the timestamp part
                    const timestampMatch = jobIdPart.match(/^(\d+)/);
                    if (timestampMatch) {
                      const timestamp = timestampMatch[1];
                      finalUrl = `/admin/apk/download/${timestamp}/${filename}`;
                    }
                  }
                }
              }

              // Proceed with download using the corrected URL

              try {
                // Use fetch to get the file
                const response = await fetch(finalUrl);

                if (!response.ok) {
                  throw new Error(`HTTP error! Status: ${response.status}`);
                }

                // Get the file as a blob
                const blob = await response.blob();

                // Create a URL for the blob
                const url = window.URL.createObjectURL(blob);

                // Create a link and click it to download
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = downloadFilename;
                document.body.appendChild(a);
                a.click();

                // Clean up
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

              } catch (error) {
                console.error(`[${id}] Error downloading ${downloadFilename}:`, error);
                const errorMessage = error instanceof Error ? error.message : String(error);
                alert(`Error downloading APK: ${errorMessage}. Please try again.`);
              }
            }}
          >
            Download Repacked APK
          </button>
        </div>
      {:else}
        <!-- Show repack button when no APK is ready -->
        <button
          type="submit"
          class="button"
          disabled={!templatePath || !configText || processing}
        >
          {#if processing}
            Processing...
          {:else}
            Repack APK
          {/if}
        </button>
      {/if}

      <!-- Fallback button (hidden by default, shown only when needed) -->
      <button
        type="button"
        class="button button-fallback"
        on:click={submitForm}
        disabled={!templatePath || !configText || processing}
        style="display: none;"
      >
        Direct Submit
      </button>
    </div>
  </form>
</div>