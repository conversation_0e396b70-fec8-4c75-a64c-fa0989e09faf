// src/lib/server/cron/utils/billingHelpers.js

/**
 * Billing utility functions for cron jobs
 */

import { getTeamRepository, getTransactionRepository, getDeviceRepository } from '../../database/DatabaseFactory.js';
import { sendNotification } from '../../notifications.js';
import { getNextBillingCycle } from './dateHelpers.js';
import { 
  getSuccessfulChargeNotification, 
  getInsufficientFundsNotification,
  getBillingErrorNotification 
} from './notificationTemplates.js';

// Global VPN handlers instance that will be set by the web server
let globalVpnHandlers = null;
let globalVpnDatabase = null;

/**
 * Set the global VPN handlers instance
 * This should be called by the web server when it starts up
 */
export function setGlobalVpnHandlers(handlers) {
  globalVpnHandlers = handlers;
  globalVpnDatabase = handlers.db;
}

/**
 * Get VPN clients using the global database instance
 * Falls back to API call if global database not available
 */
async function getVpnClients() {
  // Try to use global database first (same process)
  if (globalVpnDatabase) {
    try {
      const allClients = globalVpnDatabase.getAllClients();
      const allNodes = globalVpnDatabase.getAllNodes();
      
      if (allNodes.length > 0) {
        // nodes info (removed log)
      } else {
        // no nodes info (removed log)
      }
      return allClients;
    } catch (error) {
      // failed to use global database (removed log)
    }
  }

  // Fallback to API call
  try {
    const baseUrl = getApiBaseUrl();
    const response = await fetch(`${baseUrl}/api/clients`, {
      headers: {
        'Authorization': `Bearer ${process.env.API_KEY || 'internal'}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`API call failed: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.clients || [];
  } catch (error) {
    return [];
  }
}

/**
 * Get the correct API base URL for the current environment
 */
function getApiBaseUrl() {
  // Check if we have a custom internal API URL (for nginx setups)
  if (process.env.INTERNAL_API_URL) {
    return process.env.INTERNAL_API_URL;
  }
  
  // In production behind nginx, try the public domain
  if (process.env.NODE_ENV === 'production') {
    // Try the public URL first (through nginx)
    if (process.env.PUBLIC_URL) {
      return process.env.PUBLIC_URL;
    }
    // Fallback to localhost with the app port
    const port = process.env.PORT || 3000;
    return `http://localhost:${port}`;
  }
  
  // In development, use the dev server port
  return 'http://localhost:5173';
}

/**
 * Set VPN client status using global database or API call
 */
async function setVpnClientStatus(clientId, enabled) {
  // Try to use global database first (same process)
  if (globalVpnDatabase) {
    try {
      const result = globalVpnDatabase.updateClientStatus(clientId, enabled, 'billing-system');
      if (result) {
        return { success: true, message: `Client ${clientId} ${enabled ? 'enabled' : 'disabled'} successfully` };
      } else {
        return { success: false, error: `Client ${clientId} not found` };
      }
    } catch (error) {
      // failed to use global database (removed log)
    }
  }

  // Fallback to API call
  try {
    const baseUrl = getApiBaseUrl();
    
    const response = await fetch(`${baseUrl}/api/client/${clientId}/status`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.API_KEY || 'internal'}`
      },
      body: JSON.stringify({ enabled })
    });
    
    if (!response.ok) {
      throw new Error(`API call failed: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * Get billing configuration from environment
 * @returns {Object} Billing configuration
 */
export function getBillingConfig() {
  return {
    // Note: amount is now per-team via charge_amount field, keeping fallback for compatibility
    defaultAmount: parseFloat(process.env.BILLING_AMOUNT || '30.00'),
    cycleDays: parseInt(process.env.BILLING_CYCLE_DAYS || '30'),
    notificationEnabled: process.env.BILLING_NOTIFICATION_ENABLED !== 'false',
    timezone: process.env.BILLING_TIMEZONE || 'Europe/Kyiv',
    retryAttempts: parseInt(process.env.BILLING_RETRY_ATTEMPTS || '3'),
    batchSize: parseInt(process.env.BILLING_BATCH_SIZE || '50')
  };
}

/**
 * Process charge for a single team using the team's charge_amount
 * @param {Object} team - Team object with charge_amount field
 * @returns {Promise<Object>} Result object
 */
export async function processTeamCharge(team) {
  const teamRepository = getTeamRepository();
  const transactionRepository = getTransactionRepository();
  const config = getBillingConfig();
  
  try {
    // Use team's charge_amount, fallback to default if not set
    const amount = team.charge_amount || config.defaultAmount;
    
    // Check if team has sufficient balance
    if (team.balance < amount) {
      // Disable VPN service
      const disableResult = await disableVpnForTeam(team.id);
      if (!disableResult.success) {
        if (disableResult.errors) {
          // VPN disable errors (removed log)
        }
      }
      // Send insufficient funds notification
      if (config.notificationEnabled) {
        const devicesResult = await getDeviceRepository().findByTeamId(team.id);
        const lang = devicesResult.success && devicesResult.data.length > 0 ? (devicesResult.data[0].lang || 'en') : 'en';
        const notification = getInsufficientFundsNotification(team, amount, team.balance, lang);
        await sendBillingNotification(notification);
      }
      return {
        success: false,
        reason: 'insufficient_funds',
        teamId: team.id,
        currentBalance: team.balance,
        requiredAmount: amount,
        shortfall: amount - team.balance
      };
    }
    
    try {
      // Calculate new balance
      const newBalance = team.balance - amount;
      const nextChargeDate = getNextBillingCycle(new Date(), config.cycleDays);
      
      // Update team balance and next charge date in a transaction if possible
      const balanceUpdateResult = await teamRepository.updateBalance(team.id, newBalance);
      if (!balanceUpdateResult.success) {
        throw new Error(`Failed to update balance: ${balanceUpdateResult.error}`);
      }
      
      const chargeUpdateResult = await teamRepository.updateNextChargeAt(team.id, nextChargeDate);
      if (!chargeUpdateResult.success) {
        throw new Error(`Failed to update next charge date: ${chargeUpdateResult.error}`);
      }
      
      // Create transaction record
      const transactionData = {
        team_id: team.id,
        team_internal_id: team.internal_id,
        amount: -amount, // Negative for charge
        description: `Service payment`,
        balance_before: team.balance,
        balance_after: newBalance,
        created_at: new Date()
      };
      
      const transactionResult = await transactionRepository.create(transactionData);
      if (!transactionResult.success) {
        // Don't fail the entire operation for transaction logging failure
        }
        // Ensure VPN service is enabled
        const enableResult = await enableVpnForTeam(team.id);
        if (!enableResult.success) {
        if (enableResult.errors) {
        // VPN enable errors (removed log)
        }
        }
        // Send successful charge notification
        if (config.notificationEnabled) {
        const devicesResult = await getDeviceRepository().findByTeamId(team.id);
        const lang = devicesResult.success && devicesResult.data.length > 0 ? (devicesResult.data[0].lang || 'en') : 'en';
        const notification = getSuccessfulChargeNotification(team, amount, newBalance, nextChargeDate, lang);
        await sendBillingNotification(notification);
        }
        return {
        success: true,
        teamId: team.id,
        chargedAmount: amount,
        previousBalance: team.balance,
        newBalance: newBalance,
        nextChargeDate: nextChargeDate,
        transactionId: transactionResult.success ? transactionResult.data?.internal_id : null
        };
        } catch (error) {
        throw error; // Re-throw to be caught by the outer try-catch
        }
    
  } catch (error) {
    // Send error notification
    if (config.notificationEnabled) {
      try {
        const devicesResult = await getDeviceRepository().findByTeamId(team.id);
        const lang = devicesResult.success && devicesResult.data.length > 0 ? (devicesResult.data[0].lang || 'en') : 'en';
        const errorNotification = getBillingErrorNotification(team, error.message, 'charge_processing', lang);
        await sendBillingNotification(errorNotification);
      } catch (notificationError) {
        // failed to send error notification (removed log)
      }
    }
    return {
      success: false,
      reason: 'processing_error',
      teamId: team.id,
      error: error.message
    };
  }
}

/**
 * Enable VPN service for a team
 * @param {string} teamId - Team ID
 * @returns {Promise<{success: boolean, message: string, enabledCount?: number, errors?: Array<string>, error?: Error}>} Result of the operation
 */
export async function enableVpnForTeam(teamId) {
  try {
    // Get all VPN clients via API call to web server
    const allClients = await getVpnClients();
    
    // Get all devices for this team to map IPs to team
    const deviceRepository = getDeviceRepository();
    const devicesResult = await deviceRepository.findByTeamId(teamId);
    
    if (!devicesResult.success) {
      const message = `[enableVpnForTeam] Failed to get devices for team ${teamId}: ${devicesResult.error}`;
      return { success: false, message, error: devicesResult.error };
    }
    const teamDevices = devicesResult.data || [];
    const teamIPs = new Set(teamDevices.map(device => device.ip).filter(ip => ip));
    // Filter VPN clients that belong to this team
    // Method 1: Match by client ID (if client ID matches team ID)
    // Method 2: Match by IP addresses
    const teamClients = allClients.filter(client => {
      // Check if client ID matches team ID
      if (client.id === teamId) {
        return true;
      }
      // Check if client IPs match any team device IPs
      if (client.ips && client.ips.some(ipObj => teamIPs.has(ipObj.ip))) {
        return true;
      }
      return false;
    });
    if (!teamClients || teamClients.length === 0) {
      const message = `[enableVpnForTeam] No VPN clients found for team ${teamId}. Team has ${teamDevices.length} devices, found ${allClients.length} total VPN clients`;
      return { success: true, message };
    }
    // Enable all clients for this team
    let enabledCount = 0;
    const errors = [];
    for (const client of teamClients) {
      try {
        const result = await setVpnClientStatus(client.id, true);
        if (result.success) {
          enabledCount++;
        } else {
          const errorMsg = `Failed to enable VPN for client ${client.id}: ${result.error}`;
          errors.push(errorMsg);
        }
      } catch (clientError) {
        const errorMsg = `Error enabling VPN for client ${client.id}: ${clientError.message}`;
        errors.push(errorMsg);
      }
    }
    const message = `[enableVpnForTeam] Enabled ${enabledCount}/${teamClients.length} clients for team ${teamId}`;
    if (errors.length > 0) {
      return {
        success: false,
        message: `${message}. ${errors.length} errors occurred.`,
        enabledCount,
        errors
      };
    }
    return {
      success: true,
      message,
      enabledCount
    };
  } catch (error) {
    const errorMsg = `[enableVpnForTeam] Error enabling VPN for team ${teamId}: ${error.message}`;
    return {
      success: false,
      message: errorMsg,
      error: error
    };
  }
}

/**
 * Disable VPN service for a team
 * @param {string} teamId - Team ID
 * @returns {Promise<{success: boolean, message: string, disabledCount?: number, errors?: Array<string>, error?: Error}>} Result of the operation
 */
export async function disableVpnForTeam(teamId) {
  try {
    // Get all VPN clients via API call to web server
    const allClients = await getVpnClients();
    
    // Get all devices for this team to map IPs to team
    const deviceRepository = getDeviceRepository();
    const devicesResult = await deviceRepository.findByTeamId(teamId);
    
    if (!devicesResult.success) {
      const message = `[disableVpnForTeam] Failed to get devices for team ${teamId}: ${devicesResult.error}`;
      return { success: false, message, error: devicesResult.error };
    }
    const teamDevices = devicesResult.data || [];
    const teamIPs = new Set(teamDevices.map(device => device.ip).filter(ip => ip));
    // Filter VPN clients that belong to this team
    // Method 1: Match by client ID (if client ID matches team ID)
    // Method 2: Match by IP addresses
    const teamClients = allClients.filter(client => {
      // Check if client ID matches team ID
      if (client.id === teamId) {
        return true;
      }
      // Check if client IPs match any team device IPs
      if (client.ips && client.ips.some(ipObj => teamIPs.has(ipObj.ip))) {
        return true;
      }
      return false;
    });
    if (!teamClients || teamClients.length === 0) {
      const message = `[disableVpnForTeam] No VPN clients found for team ${teamId}. Team has ${teamDevices.length} devices, found ${allClients.length} total VPN clients`;
      return { success: true, message };
    }
    // Disable all clients for this team
    let disabledCount = 0;
    const errors = [];
    for (const client of teamClients) {
      try {
        const result = await setVpnClientStatus(client.id, false);
        if (result.success) {
          disabledCount++;
        } else {
          const errorMsg = `Failed to disable VPN for client ${client.id}: ${result.error}`;
          errors.push(errorMsg);
        }
      } catch (clientError) {
        const errorMsg = `Error disabling VPN for client ${client.id}: ${clientError.message}`;
        errors.push(errorMsg);
      }
    }
    const message = `[disableVpnForTeam] Disabled ${disabledCount}/${teamClients.length} clients for team ${teamId}`;
    if (errors.length > 0) {
      return {
        success: false,
        message: `${message}. ${errors.length} errors occurred.`,
        disabledCount,
        errors
      };
    }
    return {
      success: true,
      message,
      disabledCount
    };
  } catch (error) {
    const errorMsg = `[disableVpnForTeam] Error disabling VPN for team ${teamId}: ${error.message}`;
    return {
      success: false,
      message: errorMsg,
      error: error
    };
  }
}

/**
 * Send notification with error handling
 * @param {Object} notificationData - Notification data
 * @returns {Promise<boolean>} Success status
 */
export async function sendBillingNotification(notificationData) {
  try {
    await sendNotification(notificationData);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Process teams in batches to avoid overwhelming the database
 * @param {Array} teams - Array of teams to process
 * @param {Function} processor - Function to process each team
 * @param {number} batchSize - Batch size
 * @returns {Promise<Object>} Processing results
 */
export async function processBatch(teams, processor, batchSize = null) {
  const config = getBillingConfig();
  const actualBatchSize = batchSize || config.batchSize;
  
  const results = {
    total: teams.length,
    processed: 0,
    successful: 0,
    failed: 0,
    errors: []
  };
  
  for (let i = 0; i < teams.length; i += actualBatchSize) {
    const batch = teams.slice(i, i + actualBatchSize);
    const batchPromises = batch.map(async (team) => {
      try {
        const result = await processor(team);
        results.processed++;
        if (result.success) {
          results.successful++;
        } else {
          results.failed++;
          results.errors.push({
            teamId: team.id,
            error: result.error || result.reason
          });
        }
        return result;
      } catch (error) {
        results.processed++;
        results.failed++;
        results.errors.push({
          teamId: team.id,
          error: error.message
        });
        return { success: false, teamId: team.id, error: error.message };
      }
    });
    await Promise.all(batchPromises);
    // Small delay between batches to prevent overwhelming the system
    if (i + actualBatchSize < teams.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  return results;
}