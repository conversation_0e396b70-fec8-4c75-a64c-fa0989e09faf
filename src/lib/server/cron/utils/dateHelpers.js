// src/lib/server/cron/utils/dateHelpers.js

/**
 * Date utility functions for billing cron jobs
 * Timezone: Europe/Kyiv (as specified in requirements)
 */

/**
 * Get current date in billing timezone
 * @returns {Date} Current date in Europe/Kyiv timezone
 */
export function getCurrentBillingDate() {
  return new Date(new Date().toLocaleString("en-US", { timeZone: "Europe/Kyiv" }));
}

/**
 * Get date N days from now in billing timezone
 * @param {number} daysFromNow - Number of days to add
 * @returns {Date} Date N days from now
 */
export function getDateDaysFromNow(daysFromNow) {
  const date = getCurrentBillingDate();
  date.setDate(date.getDate() + daysFromNow);
  return date;
}

/**
 * Get start and end of day for a given date
 * @param {Date} date - Target date
 * @returns {Object} Object with startOfDay and endOfDay
 */
export function getDayBounds(date) {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);
  
  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);
  
  return { startOfDay, endOfDay };
}

/**
 * Check if a date is today in billing timezone
 * @param {Date} date - Date to check
 * @returns {boolean} True if date is today
 */
export function isToday(date) {
  const today = getCurrentBillingDate();
  const targetDate = new Date(date);
  
  return (
    today.getFullYear() === targetDate.getFullYear() &&
    today.getMonth() === targetDate.getMonth() &&
    today.getDate() === targetDate.getDate()
  );
}

/**
 * Check if a date is exactly N days from now
 * @param {Date} date - Date to check
 * @param {number} daysFromNow - Number of days from now
 * @returns {boolean} True if date is exactly N days from now
 */
export function isExactlyDaysFromNow(date, daysFromNow) {
  const targetDate = getDateDaysFromNow(daysFromNow);
  const checkDate = new Date(date);
  
  return (
    targetDate.getFullYear() === checkDate.getFullYear() &&
    targetDate.getMonth() === checkDate.getMonth() &&
    targetDate.getDate() === checkDate.getDate()
  );
}

/**
 * Get next billing cycle date
 * @param {Date} currentDate - Current date
 * @param {number} cycleDays - Billing cycle in days (default 30)
 * @returns {Date} Next billing cycle date in Europe/Kyiv timezone at 10:00 AM
 */
export function getNextBillingCycle(currentDate = new Date(), cycleDays = 30) {
  // Create a new date object to avoid modifying the original
  const date = new Date(currentDate);
  
  // Add the billing cycle days
  date.setDate(date.getDate() + cycleDays);
  
  // Set the time to 10:00 AM in Europe/Kyiv timezone
  const kyivTime = new Date(date.toLocaleString('en-US', { timeZone: 'Europe/Kyiv' }));
  kyivTime.setHours(10, 0, 0, 0);
  
  // Convert the local time back to a UTC date
  const utcDate = new Date(kyivTime.toISOString());
  
  return utcDate;
}

/**
 * Format date for logging (date only, no time)
 * @param {Date} date - Date to format
 * @returns {string} Formatted date string (MM/DD/YYYY)
 */
export function formatDateForLogging(date) {
  return new Date(date).toLocaleString("en-US", { 
    timeZone: "Europe/Kyiv",
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
}

/**
 * Get date range for finding teams with charges due in N days
 * @param {number} daysFromNow - Number of days from now
 * @returns {Object} Object with startDate and endDate
 */
export function getChargeNotificationDateRange(daysFromNow) {
  const targetDate = getDateDaysFromNow(daysFromNow);
  const { startOfDay, endOfDay } = getDayBounds(targetDate);
  
  return {
    startDate: startOfDay,
    endDate: endOfDay
  };
}