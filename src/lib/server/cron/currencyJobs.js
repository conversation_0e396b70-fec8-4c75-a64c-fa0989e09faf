// src/lib/server/cron/currencyJobs.js

import fs from 'fs';
import path from 'path';
import axios from 'axios';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the directory name equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// CoinMarketCap API configuration
const CMC_API_KEY = process.env.CMC_API_KEY;
const CMC_BASE_URL = 'https://pro-api.coinmarketcap.com/v1';

// Supported currencies and their CoinMarketCap symbols
const CURRENCY_MAPPING = {
  USDTTRC: 'USDT', // USDT TRC-20 maps to USDT symbol
  USDT: 'USDT',
  BTC: 'BTC',
  ETH: 'ETH',
  TRX: 'TRX',
  SOL: 'SOL',
  NOT: 'NOT', // Notcoin
  XMR: 'XMR',
  XRP: 'XRP',
  DOGE: 'DOGE',
};

// Fallback rates (used when API fails)
const FALLBACK_RATES = {
  USDTTRC: 1.0,
  USDT: 1.0,
  BTC: 102000,
  ETH: 2270,
  TRX: 0.08,
  SOL: 173.5,
  NOT: 0.01,
  XMR: 150,
  XRP: 2.04,
  DOGE: 0.08,
};

/**
 * Helper function to find the rates.txt file
 */
function findRatesFile() {
  // Path to the rates.txt file containing fallback exchange rates.
  // In development, it's in the project root. In production, the Vite build
  // process copies it to the parent directory of the build folder.
  const prodPath1 = path.resolve(__dirname, '../../../rates.txt');
  const prodPath2 = path.resolve(__dirname, '../../../../rates.txt');
  const devPath = path.resolve(process.cwd(), 'rates.txt');

  if (fs.existsSync(prodPath1)) {
    return prodPath1;
  } else if (fs.existsSync(prodPath2)) {
    return prodPath2;
  } else if (fs.existsSync(devPath)) {
    return devPath;
  }

  console.warn(`[CurrencyJobs] rates.txt file not found in any expected location`);
  return null;
}

/**
 * Fetch current exchange rates from CoinMarketCap API
 * @returns {Promise<Object|null>} Object with currency rates or null if failed
 */
async function fetchCurrentRates() {
  try {
    if (!CMC_API_KEY) {
      return null;
    }


    // Get all supported symbols for batch request
    const symbols = Object.values(CURRENCY_MAPPING)
      .filter((symbol, index, arr) => arr.indexOf(symbol) === index) // Remove duplicates
      .join(',');

    // Make batch API request to CoinMarketCap
    const response = await axios.get(
      `${CMC_BASE_URL}/cryptocurrency/quotes/latest`,
      {
        headers: {
          'X-CMC_PRO_API_KEY': CMC_API_KEY,
          Accept: 'application/json',
        },
        params: {
          symbol: symbols,
          convert: 'USD',
        },
        timeout: 30000, // 30 second timeout for batch request
      }
    );

    if (!response.data || !response.data.data) {
      console.error('[CurrencyJobs] Invalid response format from CoinMarketCap API');
      return null;
    }

    const rates = {};
    let updateCount = 0;

    // Map API response to our currency codes
    for (const [currency, cmcSymbol] of Object.entries(CURRENCY_MAPPING)) {
      if (response.data.data[cmcSymbol]) {
        const price = response.data.data[cmcSymbol].quote.USD.price;
        rates[currency] = price;
        updateCount++;
      } else {
        console.warn(`[CurrencyJobs] No data found for ${currency} (${cmcSymbol}), using fallback`);
        // Use fallback rate
        rates[currency] = FALLBACK_RATES[currency];
      }
    }

    return rates;

  } catch (error) {
    console.error('[CurrencyJobs] Error fetching rates from CoinMarketCap API:', error.message);
    return null;
  }
}

/**
 * Read existing rates from rates.txt file
 * @returns {Object} Existing rates or empty object
 */
function readExistingRates(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return {};
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const rates = {};
    
    const lines = content.split('\n');
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [currency, rate] = trimmedLine.split('=');
        if (currency && rate) {
          const parsedRate = parseFloat(rate);
          if (!isNaN(parsedRate)) {
            rates[currency.trim()] = parsedRate;
          }
        }
      }
    }
    
    return rates;
  } catch (error) {
    console.error('[CurrencyJobs] Error reading existing rates file:', error.message);
    return {};
  }
}

/**
 * Write rates to rates.txt file
 * @param {string} filePath - Path to rates file
 * @param {Object} rates - Rates object to write
 * @param {Object} existingRates - Existing rates for comparison
 */
function writeRatesToFile(filePath, rates, existingRates = {}) {
  try {
    const timestamp = new Date().toISOString();
    let content = `# Exchange rates updated on ${timestamp}\n`;
    content += `# Generated by currency cron job\n`;
    content += `# Format: CURRENCY=RATE_IN_USD\n\n`;

    // Sort currencies for consistent output
    const sortedCurrencies = Object.keys(rates).sort();
    
    let changedCount = 0;
    
    for (const currency of sortedCurrencies) {
      const rate = rates[currency];
      const oldRate = existingRates[currency];
      
      content += `${currency}=${rate}\n`;
      
      if (oldRate !== undefined && Math.abs(oldRate - rate) > 0.000001) {
        const change = ((rate - oldRate) / oldRate * 100).toFixed(2);
        changedCount++;
      }
    }

    fs.writeFileSync(filePath, content, 'utf8');
    
    if (changedCount > 0) {
    }
    
  } catch (error) {
    console.error('[CurrencyJobs] Error writing rates to file:', error.message);
    throw error;
  }
}

/**
 * Update currency rates job
 */
async function updateCurrencyRates() {
  try {

    // Find the rates file
    const ratesFilePath = findRatesFile();
    if (!ratesFilePath) {
      console.error('[CurrencyJobs] Could not locate rates.txt file, skipping update');
      return;
    }

    // Read existing rates for comparison
    const existingRates = readExistingRates(ratesFilePath);

    // Fetch current rates from API
    const currentRates = await fetchCurrentRates();

    if (!currentRates) {
      writeRatesToFile(ratesFilePath, FALLBACK_RATES, existingRates);
      return;
    }

    // Write updated rates to file
    writeRatesToFile(ratesFilePath, currentRates, existingRates);
    
    
  } catch (error) {
    console.error('[CurrencyJobs] Failed to update currency rates:', error.message);
  }
}

// Export for manual execution
export { updateCurrencyRates };