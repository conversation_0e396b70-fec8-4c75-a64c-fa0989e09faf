// src/lib/server/cron/callChargeJobs.js

/**
 * Call charge processing cron jobs
 */

import { getCallChargeRepository, getTeamRepository, getTransactionRepository, getDeviceRepository } from '../database/DatabaseFactory.js';
import { getBillingConfig, sendBillingNotification, processBatch } from './utils/billingHelpers.js';
import { getCallChargeNotification } from './utils/notificationTemplates.js';

/**
 * Process unprocessed call charges
 * Runs every 10 minutes to process call charges atomically
 */
export async function processCallCharges() {
  const startTime = Date.now();
  
  try {
    const config = getBillingConfig();
    const callChargeRepository = getCallChargeRepository();
    const teamRepository = getTeamRepository();
    const transactionRepository = getTransactionRepository();
    const deviceRepository = getDeviceRepository();
    
    // Atomically get and mark call charges as processed to avoid race conditions
    const callChargesResult = await callChargeRepository.atomicProcessBatch(config.batchSize);
    
    if (!callChargesResult || !callChargesResult.success) {
      console.error('[processCallCharges] Error fetching call charges:', callChargesResult?.error || 'No result returned');
      return {
        success: false,
        error: callChargesResult?.error || 'No result returned from atomicProcessBatch',
        duration: Date.now() - startTime
      };
    }
    
    const callCharges = callChargesResult.data || [];
    
    console.log(`[processCallCharges] Processing ${callCharges.length} call charges`);
    
    if (callCharges.length === 0) {
      return {
        success: true,
        processed: 0,
        charged: 0,
        errors: [],
        duration: Date.now() - startTime
      };
    }
    
    let chargedCount = 0;
    const errors = [];
    
    // Group call charges by sip_user to find associated teams
    const chargesByUser = new Map();
    for (const charge of callCharges) {
      if (!chargesByUser.has(charge.sip_user)) {
        chargesByUser.set(charge.sip_user, []);
      }
      chargesByUser.get(charge.sip_user).push(charge);
    }
    
    // Process charges for each user
    for (const [sipUser, userCharges] of chargesByUser) {
      try {
        // Find device by sip_id (call_charges.sip_user == devices.sip_id)
        const deviceResult = await deviceRepository.findBySipId(sipUser);
        
        if (!deviceResult.success || !deviceResult.data) {
          console.warn(`[processCallCharges] Device not found for sip_user: ${sipUser}, skipping charges`);
          errors.push({
            sipUser,
            error: 'Device not found',
            charges: userCharges.length
          });
          continue;
        }
        
        const device = deviceResult.data;
        
        // Find team using the device's team_id
        const teamResult = await teamRepository.findById(device.team_id);
        
        if (!teamResult.success || !teamResult.data) {
          console.warn(`[processCallCharges] Team not found for device team_id: ${device.team_id}, sip_user: ${sipUser}, skipping charges`);
          errors.push({
            sipUser,
            deviceId: device.internal_id,
            teamId: device.team_id,
            error: 'Team not found',
            charges: userCharges.length
          });
          continue;
        }
        
        const team = teamResult.data;
        
        // Calculate total charge amount for this user
        const totalChargeAmount = userCharges.reduce((sum, charge) => sum + charge.cost, 0);
        const totalDuration = userCharges.reduce((sum, charge) => sum + charge.duration, 0);
        
        if (totalChargeAmount <= 0) {
          console.log(`[processCallCharges] No charge amount for sip_user: ${sipUser}, skipping`);
          continue;
        }
        
        // Check if team has sufficient balance
        const currentBalance = parseFloat(team.balance) || 0;
        if (currentBalance < totalChargeAmount) {
          console.warn(`[processCallCharges] Insufficient balance for team ${team.id}: ${currentBalance} < ${totalChargeAmount}`);
          errors.push({
            teamId: team.id,
            sipUser,
            error: 'Insufficient balance',
            required: totalChargeAmount,
            available: currentBalance
          });
          continue;
        }
        
        // Calculate new balance
        const newBalance = currentBalance - totalChargeAmount;
        
        // Update team balance
        const balanceUpdateResult = await teamRepository.updateBalance(team.id, newBalance);
        if (!balanceUpdateResult.success) {
          console.error(`[processCallCharges] Failed to update balance for team ${team.id}:`, balanceUpdateResult.error);
          errors.push({
            teamId: team.id,
            sipUser,
            error: 'Failed to update balance',
            details: balanceUpdateResult.error
          });
          continue;
        }
        
        // Create transaction record for call charges
        const transactionData = {
          team_id: team.id,
          team_internal_id: team.internal_id,
          amount: -totalChargeAmount, // Negative for charge
          description: `Call charges for ${userCharges.length} calls (${Math.round(totalDuration)}s total)`,
          balance_before: currentBalance,
          balance_after: newBalance,
          call_charge: true, // Mark as call charge transaction
          created_at: new Date()
        };
        
        const transactionResult = await transactionRepository.create(transactionData);
        if (!transactionResult.success) {
          console.error(`[processCallCharges] Failed to create transaction for team ${team.id}:`, transactionResult.error);
          // Don't fail the entire operation for transaction logging failure
        }
        
        // Send notification if enabled
        if (config.notificationEnabled) {
          try {
            const devicesResult = await deviceRepository.findByTeamId(team.id);
            const lang = devicesResult.success && devicesResult.data.length > 0 ? (devicesResult.data[0].lang || 'en') : 'en';
            
            const notification = getCallChargeNotification(team.id, totalChargeAmount, totalDuration, newBalance, lang);
            await sendBillingNotification(notification);
          } catch (notificationError) {
            console.error(`[processCallCharges] Failed to send notification for team ${team.id}:`, notificationError);
            // Don't fail the operation for notification failure
          }
        }
        
        chargedCount++;
        console.log(`[processCallCharges] Successfully processed ${userCharges.length} charges for team ${team.id}, amount: $${totalChargeAmount.toFixed(2)}`);
        
      } catch (error) {
        console.error(`[processCallCharges] Error processing charges for sip_user ${sipUser}:`, error);
        errors.push({
          sipUser,
          error: error.message,
          charges: userCharges.length
        });
      }
    }
    
    const duration = Date.now() - startTime;
    
    return {
      success: true,
      processed: callCharges.length,
      charged: chargedCount,
      errors,
      duration
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error('[processCallCharges] Fatal error:', error);
    return {
      success: false,
      error: error.message,
      duration
    };
  }
}

/**
 * Get call charge statistics for monitoring
 * @param {Date} since - Start date for statistics
 * @param {Date} until - End date for statistics
 * @returns {Promise<Object>} Statistics object
 */
export async function getCallChargeStatistics(since, until) {
  try {
    const callChargeRepository = getCallChargeRepository();
    const statsResult = await callChargeRepository.getStatistics(since, until);
    
    if (!statsResult.success) {
      return {
        success: false,
        error: statsResult.error
      };
    }
    
    return {
      success: true,
      data: statsResult.data
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}