// src/lib/server/cron/billingJobs.js

/**
 * Billing-related cron jobs for automated charge processing
 */

import { getTeamRepository, getTransactionRepository, getDeviceRepository } from '../database/DatabaseFactory.js';
import { 
  getBillingConfig, 
  processTeamCharge, 
  sendBillingNotification, 
  processBatch 
} from './utils/billingHelpers.js';
import { 
  getTwoDayAdvanceNotification, 
  getOneDayAdvanceNotification,
  getSuccessfulChargeNotification,
  getInsufficientFundsNotification 
} from './utils/notificationTemplates.js';
import { getNextBillingCycle } from './utils/dateHelpers.js';

/**
 * Send two-day advance notifications
 * Runs daily at 9:00 AM
 */
export async function sendTwoDayAdvanceNotifications() {
  try {
    const config = getBillingConfig();
    const teamRepository = getTeamRepository();
    
    // Find teams with charges due in 2 days
    const teamsResult = await teamRepository.findTeamsWithUpcomingCharges(2);
    
    if (!teamsResult.success) {
      return {
        success: false,
        error: teamsResult.error
      };
    }
    
    const teams = teamsResult.data || [];
    
    if (teams.length === 0) {
      return {
        success: true,
        processed: 0,
        notifications: 0
      };
    }
    
    // Process notifications in batches
    const results = await processBatch(teams, async (team) => {
      try {
        if (!config.notificationEnabled) {
          return { success: true, skipped: true };
        }
        
        const devicesResult = await getDeviceRepository().findByTeamId(team.id);
        const lang = devicesResult.success && devicesResult.data.length > 0 ? (devicesResult.data[0].lang || 'en') : 'en';
        const notification = getTwoDayAdvanceNotification(team, team.next_charge_at, team.charge_amount, lang);
        const sent = await sendBillingNotification(notification);
        
        if (sent) {
          return {
            success: sent,
            teamId: team.id,
            notificationSent: sent
          };
        } else {
          return {
            success: false,
            teamId: team.id,
            error: 'Failed to send notification'
          };
        }
      } catch (error) {
        return {
          success: false,
          teamId: team.id,
          error: error.message
        };
      }
    });
    
    return {
      success: true,
      processed: results.processed,
      successful: results.successful,
      failed: results.failed,
      errors: results.errors
    };
    
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Send one-day advance notifications
 * Runs daily at 9:30 AM
 */
export async function sendOneDayAdvanceNotifications() {
  const startTime = Date.now();
  
  try {
    const config = getBillingConfig();
    const teamRepository = getTeamRepository();
    
    // Find teams with charges due in 1 day
    const teamsResult = await teamRepository.findTeamsWithUpcomingCharges(1);
    
    if (!teamsResult.success) {
      return {
        success: false,
        error: teamsResult.error,
        duration: Date.now() - startTime
      };
    }
    
    const teams = teamsResult.data || [];
    
    if (teams.length === 0) {
      return {
        success: true,
        processed: 0,
        notifications: 0,
        duration: Date.now() - startTime
      };
    }
    
    // Process notifications in batches
    const results = await processBatch(teams, async (team) => {
      try {
        if (!config.notificationEnabled) {
          return { success: true, skipped: true };
        }
        
        const devicesResult = await getDeviceRepository().findByTeamId(team.id);
        const lang = devicesResult.success && devicesResult.data.length > 0 ? (devicesResult.data[0].lang || 'en') : 'en';
        const notification = getOneDayAdvanceNotification(team, team.next_charge_at, team.charge_amount, lang);
        const sent = await sendBillingNotification(notification);
        
        return {
          success: sent,
          teamId: team.id,
          notificationSent: sent
        };
      } catch (error) {
        return {
          success: false,
          teamId: team.id,
          error: error.message
        };
      }
    });
    
    const duration = Date.now() - startTime;
    
    return {
      success: true,
      processed: results.processed,
      successful: results.successful,
      failed: results.failed,
      errors: results.errors,
      duration
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    return {
      success: false,
      error: error.message,
      duration
    };
  }
}

/**
 * Process daily charges
 * Runs daily at 10:00 AM
 */
export async function processDailyCharges() {
  const startTime = Date.now();
  
  try {
    const config = getBillingConfig();
    const teamRepository = getTeamRepository();
    
    // Find teams due for charging today
    const teamsResult = await teamRepository.findTeamsDueToday();
    
    if (!teamsResult.success) {
      return {
        success: false,
        error: teamsResult.error,
        duration: Date.now() - startTime
      };
    }
    
    const teams = teamsResult.data || [];
    
    if (teams.length === 0) {
      return {
        success: true,
        processed: 0,
        charged: 0,
        suspended: 0,
        duration: Date.now() - startTime
      };
    }
    
    let chargedCount = 0;
    let suspendedCount = 0;
    
    // Process charges in batches
    const results = await processBatch(teams, async (team) => {
      try {
        
        // Delegate all charge logic to processTeamCharge
        const chargeResult = await processTeamCharge(team);
        
        if (chargeResult.success) {
          // Successful charge
          chargedCount++;
          
          return {
            success: true,
            teamId: team.id,
            action: 'charged',
            amount: team.charge_amount,
            newBalance: chargeResult.newBalance
          };
          
        } else {
          // Insufficient funds - suspend service
          suspendedCount++;
          
          // Send insufficient funds notification
          if (config.notificationEnabled) {
            const devicesResult = await getDeviceRepository().findByTeamId(team.id);
            const lang = devicesResult.success && devicesResult.data.length > 0 ? (devicesResult.data[0].lang || 'en') : 'en';
            const notification = getInsufficientFundsNotification(
              team, 
              team.charge_amount || getBillingConfig().defaultAmount, 
              team.balance,
              lang
            );
            await sendBillingNotification(notification);
          }
          
          return {
            success: true,
            teamId: team.id,
            action: 'suspended',
            reason: 'insufficient_funds',
            currentBalance: team.balance,
            requiredAmount: team.charge_amount || getBillingConfig().defaultAmount
          };
        }
        
      } catch (error) {
        return {
          success: false,
          teamId: team.id,
          error: error.message
        };
      }
    });
    
    const duration = Date.now() - startTime;
    
    return {
      success: true,
      processed: results.processed,
      successful: results.successful,
      failed: results.failed,
      charged: chargedCount,
      suspended: suspendedCount,
      errors: results.errors,
      duration
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    return {
      success: false,
      error: error.message,
      duration
    };
  }
}