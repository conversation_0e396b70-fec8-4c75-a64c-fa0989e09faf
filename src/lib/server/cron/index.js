// src/lib/server/cron/index.js

/**
 * Main cron job initialization and management
 */

import { cron } from '@elysiajs/cron';
import {
  sendTwoDayAdvanceNotifications,
  sendOneDayAdvanceNotifications,
  processDailyCharges,
} from './billingJobs.js';
import { getBillingConfig } from './utils/billingHelpers.js';
import {
  cleanupApkBatchDirectories,
  getApkDirectoryStats,
  getApkCleanupRecommendations
} from './apkCleanupJobs.js';
import { updateCurrencyRates } from './currencyJobs.js';
import { processCallCharges } from './callChargeJobs.js';

/**
 * Initialize all billing cron jobs
 * @param {Elysia} app - Elysia application instance
 * @returns {Elysia} Modified Elysia app with cron jobs
 */
export function initializeBillingCronJobs(app) {
  const config = getBillingConfig();

  // Job 1: Two-Day Advance Notifications
  // Runs daily at 9:00 AM in Europe/Kyiv timezone
  app = app.use(
    cron({
      name: 'billing-two-day-notifications',
      pattern: '0 9 * * *', // 9:00 AM daily
      timezone: config.timezone,
      async run() {
        
        try {
          await sendTwoDayAdvanceNotifications();
        } catch (error) {
          console.error('[Cron] Error in two-day notifications job:', error);
        }
      }
    })
  );

  // Job 2: One-Day Advance Notifications  
  // Runs daily at 9:30 AM in Europe/Kyiv timezone
  app = app.use(
    cron({
      name: 'billing-one-day-notifications',
      pattern: '30 9 * * *', // 9:30 AM daily
      timezone: config.timezone,
      async run() {
        
        try {
          await sendOneDayAdvanceNotifications();
        } catch (error) {
          console.error('[Cron] Error in one-day notifications job:', error);
        }
      }
    })
  );

  // Job 3: Charge Processing
  // Runs every hour to process pending charges
  app = app.use(
    cron({
      name: 'billing-daily-charges',
      pattern: '0 * * * *', // At minute 0 of every hour
      timezone: config.timezone,
      async run() {
        
        try {
          await processDailyCharges();          
        } catch (error) {
          console.error('[Cron] Error in daily charges job:', error);
        }
      }
    })
  );

  // Job 4: APK Cleanup Job
  // Runs daily at midnight to clean up old APK batch directories
  app = app.use(
    cron({
      name: 'apk-cleanup',
      pattern: '0 0 * * *', // Daily at midnight
      timezone: config.timezone,
      async run() {
        try {
          // Get current stats before cleanup
          const statsBefore = await getApkDirectoryStats();

          // Perform cleanup (remove directories older than 1 day)
          const result = await cleanupApkBatchDirectories(1);

          if (result.success) {
            if (result.errors.length > 0) {
              console.warn(`[APK Cleanup] Encountered ${result.errors.length} errors during cleanup:`, result.errors);
            }
          } else {
            console.error('[APK Cleanup] Cleanup failed:', result.errors);
          }

          // Get stats after cleanup
          const statsAfter = await getApkDirectoryStats();

          // Check if further cleanup is recommended
          const recommendations = await getApkCleanupRecommendations();
          if (recommendations.shouldCleanup) {
            console.warn(`[APK Cleanup] Cleanup recommendation: ${recommendations.reason}`);
          }

        } catch (error) {
          console.error('[APK Cleanup] Error in APK cleanup job:', error);
        }
      }
    })
  );

  // Job 5: Currency Rates Update Job
  // Runs daily at 2:00 AM to update exchange rates
  app = app.use(
    cron({
      name: 'currency-rates-update',
      pattern: '0 2 * * *', // Daily at 2:00 AM
      timezone: config.timezone,
      async run() {
        try {
          await updateCurrencyRates();
        } catch (error) {
          console.error('[Currency Update] Error during currency rates update job:', error);
        }
      }
    })
  );

  // Job 6: Call Charge Processing Job
  // Runs at configurable intervals to process unprocessed call charges
  const callChargePattern = process.env.CALL_CHARGE_CRON_PATTERN || '*/3 * * * *';
  app = app.use(
    cron({
      name: 'call-charge-processing',
      pattern: callChargePattern,
      timezone: config.timezone,
      async run() {
        try {
          const result = await processCallCharges();
          if (result.success) {
            if (result.processed > 0) {
              console.log(`[Call Charges] Processed ${result.processed} charges, charged ${result.charged} teams in ${result.duration}ms`);
            }
            if (result.errors && result.errors.length > 0) {
              console.warn(`[Call Charges] Encountered ${result.errors.length} errors:`, result.errors);
            }
          } else {
            console.error('[Call Charges] Processing failed:', result.error);
          }
        } catch (error) {
          console.error('[Call Charges] Error during call charge processing job:', error);
        }
      }
    })
  );

  return app;
}

/**
 * Get cron job status and control functions
 * @param {Elysia} app - Elysia application instance with cron jobs
 * @returns {Object} Cron job control functions
 */
export function getBillingCronControls(app) {
  return {
    /**
     * Stop all billing cron jobs
     */
    stopAll() {
      try {
        const cronStore = app.store?.cron;
        if (cronStore) {
          if (cronStore['billing-two-day-notifications']) {
            cronStore['billing-two-day-notifications'].stop();
          }
          if (cronStore['billing-one-day-notifications']) {
            cronStore['billing-one-day-notifications'].stop();
          }
          if (cronStore['billing-daily-charges']) {
            cronStore['billing-daily-charges'].stop();
          }
          if (cronStore['billing-statistics']) {
            cronStore['billing-statistics'].stop();
          }
          if (cronStore['apk-cleanup']) {
            cronStore['apk-cleanup'].stop();
          }
          if (cronStore['currency-rates-update']) {
            cronStore['currency-rates-update'].stop();
          }
          if (cronStore['call-charge-processing']) {
            cronStore['call-charge-processing'].stop();
          }
        }
      } catch (error) {
        console.error('[getBillingCronControls] Error stopping cron jobs:', error);
      }
    },

    /**
     * Start all billing cron jobs
     */
    startAll() {
      try {
        const cronStore = app.store?.cron;
        if (cronStore) {
          if (cronStore['billing-two-day-notifications']) {
            cronStore['billing-two-day-notifications'].start();
          }
          if (cronStore['billing-one-day-notifications']) {
            cronStore['billing-one-day-notifications'].start();
          }
          if (cronStore['billing-daily-charges']) {
            cronStore['billing-daily-charges'].start();
          }
          if (cronStore['billing-statistics']) {
            cronStore['billing-statistics'].start();
          }
          if (cronStore['apk-cleanup']) {
            cronStore['apk-cleanup'].start();
          }
          if (cronStore['currency-rates-update']) {
            cronStore['currency-rates-update'].start();
          }
          if (cronStore['call-charge-processing']) {
            cronStore['call-charge-processing'].start();
          }
        }
      } catch (error) {
        console.error('[getBillingCronControls] Error starting cron jobs:', error);
      }
    },

    /**
     * Get status of all billing cron jobs
     */
    getStatus() {
      try {
        const cronStore = app.store?.cron;
        if (!cronStore) {
          return { error: 'Cron store not available' };
        }

        return {
          'billing-two-day-notifications': cronStore['billing-two-day-notifications'] ? 'active' : 'inactive',
          'billing-one-day-notifications': cronStore['billing-one-day-notifications'] ? 'active' : 'inactive',
          'billing-daily-charges': cronStore['billing-daily-charges'] ? 'active' : 'inactive',
          'billing-statistics': cronStore['billing-statistics'] ? 'active' : 'inactive',
          'apk-cleanup': cronStore['apk-cleanup'] ? 'active' : 'inactive',
          'currency-rates-update': cronStore['currency-rates-update'] ? 'active' : 'inactive',
          'call-charge-processing': cronStore['call-charge-processing'] ? 'active' : 'inactive'
        };
      } catch (error) {
        console.error('[getBillingCronControls] Error getting status:', error);
        return { error: error.message };
      }
    },

    /**
     * Run all billing jobs once immediately
     */
    async runAllJobs() {
      try {
        // Run each job in sequence to avoid overloading the system
        await new Promise((resolve) => setTimeout(resolve, 20000));
        await this.triggerJob('two-day-notifications');
        await this.triggerJob('one-day-notifications');
        await this.triggerJob('daily-charges');
        await this.triggerJob('statistics');
        await this.triggerJob('apk-cleanup');
        await this.triggerJob('currency-rates-update');
        await this.triggerJob('call-charge-processing');
        return { success: true };
      } catch (error) {
        console.error('[getBillingCronControls] Error running jobs at startup:', error);
        return { success: false, error: error.message };
      }
    },

    /**
     * Manually trigger a specific job (for testing)
     */
    async triggerJob(jobName) {
      try {
        switch (jobName) {
          case 'two-day-notifications':
            return await sendTwoDayAdvanceNotifications();
          case 'one-day-notifications':
            return await sendOneDayAdvanceNotifications();
          case 'daily-charges':
            return await processDailyCharges();
          case 'apk-cleanup':
            return await cleanupApkBatchDirectories(1);
          case 'currency-rates-update':
            return await updateCurrencyRates();
          case 'call-charge-processing':
            return await processCallCharges();
          default:
            throw new Error(`Unknown job name: ${jobName}`);
        }
      } catch (error) {
        console.error(`[getBillingCronControls] Error triggering job ${jobName}:`, error);
        return { success: false, error: error.message };
      }
    }
  };
}