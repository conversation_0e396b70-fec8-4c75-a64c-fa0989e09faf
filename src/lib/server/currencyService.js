// Currency conversion service for cryptocurrency to USD conversion
import { env } from '$env/dynamic/private';
import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// CoinMarketCap API configuration
const CMC_API_KEY = env.CMC_API_KEY;
const CMC_BASE_URL = 'https://pro-api.coinmarketcap.com/v1';

// Fallback exchange rates (used when API is unavailable)
const FALLBACK_RATES = {
  USDTTRC: 1.0,
  USDT: 1.0,
  BTC: 102000,
  ETH: 2270,
  TRX: 0.08,
  SOL: 173.5,
  NOT: 0.01,
  XMR: 150,
  XRP: 2.04,
  DOGE: 0.08,
};

// Type for supported currencies
const SUPPORTED_CURRENCIES = Object.keys(FALLBACK_RATES);

// Helper function to find the rates.txt file
function findRatesFile() {
  // Path to the rates.txt file containing fallback exchange rates.
  // In development, it's in the project root. In production, the Vite build
  // process copies it to the parent directory of the build folder.
  const prodPath1 = path.resolve(__dirname, '../../../rates.txt');
  const prodPath2 = path.resolve(__dirname, '../../../../rates.txt');
  const devPath = path.resolve(process.cwd(), 'rates.txt');

  if (fs.existsSync(prodPath1)) {
    return prodPath1;
  } else if (fs.existsSync(prodPath2)) {
    return prodPath2;
  } else if (fs.existsSync(devPath)) {
    return devPath;
  }

  console.warn(`[CurrencyService] rates.txt file not found in any expected location`);
  return null;
}

// Parse rates from rates.txt file
function parseRatesFromFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const rates = {};
    
    const lines = content.split('\n');
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [currency, rate] = trimmedLine.split('=');
        if (currency && rate) {
          const parsedRate = parseFloat(rate);
          if (!isNaN(parsedRate)) {
            rates[currency.trim()] = parsedRate;
          }
        }
      }
    }
    
    return rates;
  } catch (error) {
    console.error(`[CurrencyService] Error reading rates file:`, error);
    return null;
  }
}

// Get fallback rates from file or hardcoded values
function getFallbackRates() {
  const ratesFilePath = findRatesFile();
  if (ratesFilePath) {
    const fileRates = parseRatesFromFile(ratesFilePath);
    if (fileRates && Object.keys(fileRates).length > 0) {
      return fileRates;
    }
  }
  
  return FALLBACK_RATES;
}

/**
 * Get exchange rate for a cryptocurrency to USD
 * @param {string} currency - Currency code (e.g., 'BTC', 'USDTTRC')
 * @returns {Promise<number>} Exchange rate to USD
 */
export async function getExchangeRate(currency) {
  try {
    // For stablecoins pegged to USD, return 1.0
    if (currency === 'USDTTRC' || currency === 'USDT' || currency === 'USDC') {
      return 1.0;
    }

    // Try to get real-time rates from an API (you can implement this)
    const realTimeRate = await getRealTimeRate(currency);
    if (realTimeRate) {
      return realTimeRate;
    }

    // Fall back to rates from file or hardcoded rates
    const fallbackRates = getFallbackRates();
    const fallbackRate = fallbackRates[currency];
    if (fallbackRate !== undefined) {
      return fallbackRate;
    }

    // If no rate found, log warning and return 0
    console.warn(
      `[CurrencyService] No exchange rate found for currency: ${currency}`
    );
    return 0;
  } catch (error) {
    console.error(
      `[CurrencyService] Error getting exchange rate for ${currency}:`,
      error
    );
    const fallbackRates = getFallbackRates();
    const fallbackRate = fallbackRates[currency];
    return fallbackRate !== undefined ? fallbackRate : 0;
  }
}

/**
 * Convert cryptocurrency amount to USD
 * @param {number} amount - Amount in cryptocurrency
 * @param {string} currency - Currency code
 * @returns {Promise<number>} Amount in USD
 */
export async function convertToUSD(amount, currency) {
  try {
    const rate = await getExchangeRate(currency);
    const usdAmount = amount * rate;

    return usdAmount;
  } catch (error) {
    console.error(
      `[CurrencyService] Error converting ${amount} ${currency} to USD:`,
      error
    );
    
    // Try to get fallback rate directly from file/hardcoded values
    try {
      const fallbackRates = getFallbackRates();
      const fallbackRate = fallbackRates[currency];
      if (fallbackRate !== undefined) {
        const fallbackUsdAmount = amount * fallbackRate;
        return fallbackUsdAmount;
      }
    } catch (fallbackError) {
      console.error(
        `[CurrencyService] Error using fallback rates for ${currency}:`,
        fallbackError
      );
    }
    
    return 0;
  }
}

/**
 * Get real-time exchange rate from CoinMarketCap API
 * @param {string} currency - Currency code
 * @returns {Promise<number|null>} Exchange rate or null if failed
 */
async function getRealTimeRate(currency) {
  try {
    // Skip API call if no API key is configured
    if (!CMC_API_KEY) {
      return null;
    }

    // Get CoinMarketCap symbol for the currency
    const cmcSymbol = getCoinMarketCapSymbol(currency);
    if (!cmcSymbol) {
      return null;
    }

    // Make API request to CoinMarketCap
    const response = await axios.get(
      `${CMC_BASE_URL}/cryptocurrency/quotes/latest`,
      {
        headers: {
          'X-CMC_PRO_API_KEY': CMC_API_KEY,
          Accept: 'application/json',
        },
        params: {
          symbol: cmcSymbol,
          convert: 'USD',
        },
        timeout: 10000, // 10 second timeout
      }
    );

    if (response.data && response.data.data && response.data.data[cmcSymbol]) {
      const price = response.data.data[cmcSymbol].quote.USD.price;
      return price;
    }

    console.warn(
      `[CurrencyService] No price data found for ${currency} (${cmcSymbol}) in CMC response`
    );
    return null;
  } catch (error) {
    // Simplified error handling
    console.error(
      `[CurrencyService] Error fetching real-time rate for ${currency}:`,
      error
    );
    return null;
  }
}

/**
 * Map currency codes to CoinMarketCap symbols
 * @param {string} currency - Currency code
 * @returns {string|null} CoinMarketCap symbol or null if not supported
 */
function getCoinMarketCapSymbol(currency) {
  const mapping = {
    BTC: 'BTC',
    ETH: 'ETH',
    TRX: 'TRX',
    SOL: 'SOL',
    XMR: 'XMR',
    XRP: 'XRP',
    DOGE: 'DOGE',
    USDTTRC: 'USDT', // USDT TRC-20 maps to USDT symbol
    USDT: 'USDT',
    NOT: 'NOT', // Notcoin
  };

  return mapping[currency] || null;
}

/**
 * Update exchange rates from CoinMarketCap API (can be called periodically)
 * @returns {Promise<Object>} Updated rates object
 */
export async function updateExchangeRates() {
  try {

    if (!CMC_API_KEY) {
      return FALLBACK_RATES;
    }

    // Get all supported symbols for batch request
    const symbols = Object.keys(FALLBACK_RATES)
      .map((currency) => getCoinMarketCapSymbol(currency))
      .filter((symbol) => symbol !== null)
      .join(',');

    if (!symbols) {
  
      return FALLBACK_RATES;
    }

    // Make batch API request to CoinMarketCap
    const response = await axios.get(
      `${CMC_BASE_URL}/cryptocurrency/quotes/latest`,
      {
        headers: {
          'X-CMC_PRO_API_KEY': CMC_API_KEY,
          Accept: 'application/json',
        },
        params: {
          symbol: symbols,
          convert: 'USD',
        },
        timeout: 15000, // 15 second timeout for batch request
      }
    );

    const updatedRates = { ...FALLBACK_RATES };
    let updateCount = 0;

    if (response.data && response.data.data) {
      // Update rates with API data
      for (const currency of Object.keys(FALLBACK_RATES)) {
        const cmcSymbol = getCoinMarketCapSymbol(currency);
        if (cmcSymbol && response.data.data[cmcSymbol]) {
          const price = response.data.data[cmcSymbol].quote.USD.price;
          updatedRates[currency] = price;
          updateCount++;
        }
      }
    }

    return updatedRates;
  } catch (error) {
    console.error('[CurrencyService] Error updating exchange rates:', error);
    return FALLBACK_RATES;
  }
}

/**
 * Get all supported currencies and their current rates
 
 */
export async function getAllRates() {
  try {
    const rates = {};

    for (const currency of Object.keys(FALLBACK_RATES)) {
      rates[currency] = await getExchangeRate(currency);
    }

    return rates;
  } catch (error) {
    console.error('[CurrencyService] Error getting all rates:', error);
    return { ...FALLBACK_RATES };
  }
}

/**
 * Test CoinMarketCap API connection
 * @returns {Promise<Object>} Test results
 */
export async function testCoinMarketCapAPI() {
  try {

    if (!CMC_API_KEY) {
      return {
        success: false,
        error: 'No CMC_API_KEY environment variable configured',
        suggestion:
          'Set CMC_API_KEY environment variable with your CoinMarketCap API key',
      };
    }

    // Test with a simple BTC price request
    const response = await axios.get(
      `${CMC_BASE_URL}/cryptocurrency/quotes/latest`,
      {
        headers: {
          'X-CMC_PRO_API_KEY': CMC_API_KEY,
          Accept: 'application/json',
        },
        params: {
          symbol: 'BTC',
          convert: 'USD',
        },
        timeout: 10000,
      }
    );

    if (response.data && response.data.data && response.data.data.BTC) {
      const btcPrice = response.data.data.BTC.quote.USD.price;
      return {
        success: true,
        message: 'CoinMarketCap API connection successful',
        testPrice: `BTC: $${btcPrice.toFixed(2)}`,
        apiKeyConfigured: true,
      };
    } else {
      return {
        success: false,
        error: 'Unexpected response format from CoinMarketCap API',
        response: response.data,
      };
    }
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    return {
      success: false,
      error: 'Failed to connect to CoinMarketCap API',
      details: errorMessage,
      apiKeyConfigured: !!CMC_API_KEY,
    };
  }
}
