import { IRepository } from './IRepository.js';

/**
 * Interface for CallCharge repository operations
 */
export class ICallChargeRepository extends IRepository {
  /**
   * Find unprocessed call charges
   * @param {number} limit - Maximum number of records to return
   * @returns {Promise<QueryResult>}
   */
  async findUnprocessed(limit = 100) {
    throw new Error('findUnprocessed() method must be implemented');
  }

  /**
   * Mark call charges as processed atomically
   * @param {string[]} cdrIds - Array of CDR IDs to mark as processed
   * @returns {Promise<QueryResult>}
   */
  async markAsProcessed(cdrIds) {
    throw new Error('markAsProcessed() method must be implemented');
  }

  /**
   * Find call charges by SIP user
   * @param {string} sipUser - SIP user identifier
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findBySipUser(sipUser, options) {
    throw new Error('findBySipUser() method must be implemented');
  }

  /**
   * Get call charge statistics
   * @param {Date} since - Start date
   * @param {Date} until - End date
   * @returns {Promise<QueryResult>}
   */
  async getStatistics(since, until) {
    throw new Error('getStatistics() method must be implemented');
  }

  /**
   * Atomically process call charges (mark as processed and return the records)
   * This method should use database-level locking to prevent race conditions
   * @param {number} limit - Maximum number of records to process
   * @returns {Promise<QueryResult>} Result containing the processed call charges
   */
  async atomicProcessBatch(limit = 100) {
    throw new Error('atomicProcessBatch() method must be implemented');
  }
}