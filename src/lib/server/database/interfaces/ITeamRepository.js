import { IRepository } from './IRepository.js';
import { QueryOptions, QueryResult } from './IDatabase.js';

/**
 * Team repository interface
 * Defines all team-related database operations
 */
export class ITeamRepository extends IRepository {
  /**
   * Find team by public ID
   * @param {string} teamId - Team's public ID
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByTeamId(teamId, options = new QueryOptions()) {
    throw new Error('findByTeamId() method must be implemented');
  }

  /**
   * Find teams with balance above threshold
   * @param {number} threshold - Minimum balance threshold
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findTeamsWithBalance(threshold = 0, options = new QueryOptions()) {
    throw new Error('findTeamsWithBalance() method must be implemented');
  }

  /**
   * Find teams with balance below threshold (low balance teams)
   * @param {number} threshold - Maximum balance threshold
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findLowBalanceTeams(threshold = 10, options = new QueryOptions()) {
    throw new Error('findLowBalanceTeams() method must be implemented');
  }

  /**
   * Find top teams by balance
   * @param {number} limit - Number of teams to return
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findTopTeamsByBalance(limit = 10, options = new QueryOptions()) {
    throw new Error('findTopTeamsByBalance() method must be implemented');
  }

  /**
   * Find recently created teams
   * @param {Date} since - Date threshold for recent teams
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findRecentTeams(since, options = new QueryOptions()) {
    throw new Error('findRecentTeams() method must be implemented');
  }

  /**
   * Update team balance
   * @param {string} teamId - Team's public ID
   * @param {number} newBalance - New balance amount
   * @returns {Promise<QueryResult>}
   */
  async updateBalance(teamId, newBalance) {
    throw new Error('updateBalance() method must be implemented');
  }

  /**
   * Increment team balance by amount
   * @param {string} teamId - Team's public ID
   * @param {number} amount - Amount to add (can be negative)
   * @returns {Promise<QueryResult>}
   */
  async incrementBalance(teamId, amount) {
    throw new Error('incrementBalance() method must be implemented');
  }

  /**
   * Get total balance across all teams
   * @returns {Promise<QueryResult>}
   */
  async getTotalBalance() {
    throw new Error('getTotalBalance() method must be implemented');
  }

  /**
   * Get team statistics
   * @param {Object} options - Statistics options
   * @param {Date} options.since - Date threshold for recent data
   * @param {number} options.lowBalanceThreshold - Threshold for low balance teams
   * @param {number} options.topTeamsLimit - Number of top teams to include
   * @returns {Promise<QueryResult>}
   */
  async getStatistics(options = {}) {
    throw new Error('getStatistics() method must be implemented');
  }

  /**
   * Delete team and all associated data
   * @param {string} teamId - Team's public ID
   * @returns {Promise<QueryResult>}
   */
  async deleteTeamWithAssociatedData(teamId) {
    throw new Error('deleteTeamWithAssociatedData() method must be implemented');
  }

  /**
   * Check if team exists by public ID
   * @param {string} teamId - Team's public ID
   * @returns {Promise<boolean>}
   */
  async existsByTeamId(teamId) {
    const result = await this.count(new QueryOptions({ where: { id: teamId } }));
    return result.success && result.count > 0;
  }

  /**
   * Find teams by owner
   * @param {string} ownerId - Owner's public ID
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByOwner(ownerId, options = new QueryOptions()) {
    throw new Error('findByOwner() method must be implemented');
  }

  /**
   * Update next charge date
   * @param {string} teamId - Team's public ID
   * @param {Date} nextChargeAt - Next charge timestamp
   * @returns {Promise<QueryResult>}
   */
  async updateNextChargeAt(teamId, nextChargeAt) {
    throw new Error('updateNextChargeAt() method must be implemented');
  }

  /**
   * Update team charge amount
   * @param {string} teamId - Team's public ID
   * @param {number} chargeAmount - New charge amount
   * @returns {Promise<QueryResult>}
   */
  async updateChargeAmount(teamId, chargeAmount) {
    throw new Error('updateChargeAmount() method must be implemented');
  }

  /**
   * Find teams due for charging
   * @param {Date} beforeDate - Find teams with next_charge_at before this date
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findTeamsDueForCharging(beforeDate = new Date(), options = new QueryOptions()) {
    throw new Error('findTeamsDueForCharging() method must be implemented');
  }

  /**
   * Get balance history for a team (requires transaction data)
   * @param {string} teamId - Team's public ID
   * @param {Date} since - Start date for history
   * @param {Date} until - End date for history
   * @returns {Promise<QueryResult>}
   */
  async getBalanceHistory(teamId, since, until = new Date()) {
    throw new Error('getBalanceHistory() method must be implemented');
  }

  /**
   * Bulk update team balances
   * @param {Array<{teamId: string, balance: number}>} updates - Array of balance updates
   * @returns {Promise<QueryResult>}
   */
  async bulkUpdateBalances(updates) {
    throw new Error('bulkUpdateBalances() method must be implemented');
  }

  /**
   * Get table name for this repository
   * @returns {string}
   */
  getTableName() {
    return 'teams';
  }
}
