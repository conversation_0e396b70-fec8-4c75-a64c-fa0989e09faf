import { QueryResult, QueryOptions } from './IDatabase.js';

/**
 * Base repository interface that all entity repositories must implement
 * Provides standard CRUD operations and common query patterns
 * @template T - The entity type
 */
export class IRepository {
  /**
   * @param {import('./IDatabase.js').IDatabase} database - Database instance
   */
  constructor(database) {
    this.database = database;
  }

  /**
   * Find a single entity by ID
   * @param {string|number} id - Entity ID
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findById(id, options = new QueryOptions()) {
    throw new Error('findById() method must be implemented');
  }

  /**
   * Find multiple entities with optional filtering
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findMany(options = new QueryOptions()) {
    throw new Error('findMany() method must be implemented');
  }

  /**
   * Find a single entity with optional filtering
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findOne(options = new QueryOptions()) {
    throw new Error('findOne() method must be implemented');
  }

  /**
   * Create a new entity
   * @param {Partial<T>} data - Entity data
   * @returns {Promise<QueryResult>}
   */
  async create(data) {
    throw new Error('create() method must be implemented');
  }

  /**
   * Create multiple entities
   * @param {Partial<T>[]} data - Array of entity data
   * @returns {Promise<QueryResult>}
   */
  async createMany(data) {
    throw new Error('createMany() method must be implemented');
  }

  /**
   * Update an entity by ID
   * @param {string|number} id - Entity ID
   * @param {Partial<T>} data - Update data
   * @returns {Promise<QueryResult>}
   */
  async updateById(id, data) {
    throw new Error('updateById() method must be implemented');
  }

  /**
   * Update multiple entities with filtering
   * @param {QueryOptions} options - Query options for filtering
   * @param {Partial<T>} data - Update data
   * @returns {Promise<QueryResult>}
   */
  async updateMany(options, data) {
    throw new Error('updateMany() method must be implemented');
  }

  /**
   * Delete an entity by ID
   * @param {string|number} id - Entity ID
   * @returns {Promise<QueryResult>}
   */
  async deleteById(id) {
    throw new Error('deleteById() method must be implemented');
  }

  /**
   * Delete multiple entities with filtering
   * @param {QueryOptions} options - Query options for filtering
   * @returns {Promise<QueryResult>}
   */
  async deleteMany(options) {
    throw new Error('deleteMany() method must be implemented');
  }

  /**
   * Count entities with optional filtering
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async count(options = new QueryOptions()) {
    throw new Error('count() method must be implemented');
  }

  /**
   * Check if an entity exists
   * @param {string|number} id - Entity ID
   * @returns {Promise<boolean>}
   */
  async exists(id) {
    const result = await this.count(new QueryOptions({ where: { id } }));
    return result.success && result.count > 0;
  }

  /**
   * Execute a custom query specific to this repository
   * @param {string} queryName - Name of the custom query
   * @param {any} params - Query parameters
   * @returns {Promise<QueryResult>}
   */
  async executeCustomQuery(queryName, params = {}) {
    throw new Error('executeCustomQuery() method must be implemented');
  }

  /**
   * Get the table/collection name for this repository
   * @returns {string}
   */
  getTableName() {
    throw new Error('getTableName() method must be implemented');
  }

  /**
   * Validate entity data before operations
   * @param {Partial<T>} data - Entity data to validate
   * @param {string} operation - Operation type (create, update)
   * @returns {Promise<{valid: boolean, errors: string[]}>}
   */
  async validate(data, operation = 'create') {
    // Default implementation - can be overridden
    return { valid: true, errors: [] };
  }

  /**
   * Transform raw database data to entity format
   * @param {any} rawData - Raw data from database
   * @returns {T}
   */
  transformFromDb(rawData) {
    // Default implementation - can be overridden
    return rawData;
  }

  /**
   * Transform entity data to database format
   * @param {Partial<T>} entityData - Entity data
   * @returns {any}
   */
  transformToDb(entityData) {
    // Default implementation - can be overridden
    return entityData;
  }
}
