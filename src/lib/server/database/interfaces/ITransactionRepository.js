import { IRepository } from './IRepository.js';
import { QueryOptions, QueryResult } from './IDatabase.js';

/**
 * Transaction repository interface
 * Defines all transaction-related database operations
 */
export class ITransactionRepository extends IRepository {
  /**
   * Find transactions by team ID
   * @param {string} teamId - Team's public ID
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByTeamId(teamId, options = new QueryOptions()) {
    throw new Error('findByTeamId() method must be implemented');
  }

  /**
   * Find transactions within date range
   * @param {Date} since - Start date
   * @param {Date} until - End date
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByDateRange(since, until, options = new QueryOptions()) {
    throw new Error('findByDateRange() method must be implemented');
  }

  /**
   * Find recent transactions
   * @param {Date} since - Date threshold for recent transactions
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findRecent(since, options = new QueryOptions()) {
    throw new Error('findRecent() method must be implemented');
  }

  /**
   * Find transactions by amount range
   * @param {number} minAmount - Minimum amount
   * @param {number} maxAmount - Maximum amount
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByAmountRange(minAmount, maxAmount, options = new QueryOptions()) {
    throw new Error('findByAmountRange() method must be implemented');
  }

  /**
   * Find transactions with team information (joined data)
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findWithTeamInfo(options = new QueryOptions()) {
    throw new Error('findWithTeamInfo() method must be implemented');
  }

  /**
   * Get transaction statistics
   * @param {Object} options - Statistics options
   * @param {Date} options.since - Date threshold for recent data
   * @param {Date} options.until - End date for data
   * @returns {Promise<QueryResult>}
   */
  async getStatistics(options = {}) {
    throw new Error('getStatistics() method must be implemented');
  }

  /**
   * Get total transaction volume
   * @param {Date} since - Start date (optional)
   * @param {Date} until - End date (optional)
   * @returns {Promise<QueryResult>}
   */
  async getTotalVolume(since = null, until = null) {
    throw new Error('getTotalVolume() method must be implemented');
  }

  /**
   * Get transaction trends (grouped by date)
   * @param {Date} since - Start date
   * @param {Date} until - End date
   * @param {string} groupBy - Group by period ('day', 'week', 'month')
   * @returns {Promise<QueryResult>}
   */
  async getTransactionTrends(since, until, groupBy = 'day') {
    throw new Error('getTransactionTrends() method must be implemented');
  }

  /**
   * Get average transaction amount
   * @param {Date} since - Start date (optional)
   * @param {Date} until - End date (optional)
   * @returns {Promise<QueryResult>}
   */
  async getAverageAmount(since = null, until = null) {
    throw new Error('getAverageAmount() method must be implemented');
  }

  /**
   * Find largest transactions
   * @param {number} limit - Number of transactions to return
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findLargestTransactions(limit = 10, options = new QueryOptions()) {
    throw new Error('findLargestTransactions() method must be implemented');
  }

  /**
   * Create transaction with balance validation
   * @param {Object} transactionData - Transaction data
   * @param {boolean} validateBalance - Whether to validate balance calculation
   * @returns {Promise<QueryResult>}
   */
  async createWithValidation(transactionData, validateBalance = true) {
    throw new Error('createWithValidation() method must be implemented');
  }

  /**
   * Check if transaction exists by internal ID
   * @param {number} internalId - Transaction internal ID
   * @returns {Promise<boolean>}
   */
  async existsByInternalId(internalId) {
    const result = await this.count(new QueryOptions({ where: { internal_id: internalId } }));
    return result.success && result.count > 0;
  }

  /**
   * Get balance history for a team
   * @param {string} teamId - Team's public ID
   * @param {Date} since - Start date
   * @param {Date} until - End date
   * @returns {Promise<QueryResult>}
   */
  async getBalanceHistory(teamId, since, until = new Date()) {
    throw new Error('getBalanceHistory() method must be implemented');
  }

  /**
   * Find credit transactions (positive amounts)
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findCredits(options = new QueryOptions()) {
    throw new Error('findCredits() method must be implemented');
  }

  /**
   * Find debit transactions (negative amounts)
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findDebits(options = new QueryOptions()) {
    throw new Error('findDebits() method must be implemented');
  }

  /**
   * Get monthly transaction summary
   * @param {number} year - Year
   * @param {number} month - Month (1-12)
   * @returns {Promise<QueryResult>}
   */
  async getMonthlyTransactionSummary(year, month) {
    throw new Error('getMonthlyTransactionSummary() method must be implemented');
  }

  /**
   * Delete transactions by team ID
   * @param {string} teamId - Team's public ID
   * @returns {Promise<QueryResult>}
   */
  async deleteByTeamId(teamId) {
    throw new Error('deleteByTeamId() method must be implemented');
  }

  /**
   * Find transactions by description pattern
   * @param {string} pattern - Description search pattern
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByDescriptionPattern(pattern, options = new QueryOptions()) {
    throw new Error('findByDescriptionPattern() method must be implemented');
  }

  /**
   * Get transaction count by type
   * @param {Date} since - Start date (optional)
   * @param {Date} until - End date (optional)
   * @returns {Promise<QueryResult>}
   */
  async getCountByType(since = null, until = null) {
    throw new Error('getCountByType() method must be implemented');
  }

  /**
   * Find duplicate transactions (same amount, team, and close timestamps)
   * @param {number} timeThresholdMinutes - Time threshold in minutes for duplicates
   * @returns {Promise<QueryResult>}
   */
  async findDuplicateTransactions(timeThresholdMinutes = 5) {
    throw new Error('findDuplicateTransactions() method must be implemented');
  }

  /**
   * Get table name for this repository
   * @returns {string}
   */
  getTableName() {
    return 'transactions';
  }
}
