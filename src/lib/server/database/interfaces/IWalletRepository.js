import { IRepository } from './IRepository.js';
import { QueryOptions, QueryResult } from './IDatabase.js';

/**
 * Wallet repository interface
 * Defines all wallet-related database operations
 */
export class IWalletRepository extends IRepository {
  /**
   * Find wallets by team ID
   * @param {string} teamId - Team's public ID
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByTeamId(teamId, options = new QueryOptions()) {
    throw new Error('findByTeamId() method must be implemented');
  }

  /**
   * Find wallet by team ID and currency
   * @param {string} teamId - Team's public ID
   * @param {string} currency - Cryptocurrency currency code
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByTeamIdAndCurrency(teamId, currency, options = new QueryOptions()) {
    throw new Error('findByTeamIdAndCurrency() method must be implemented');
  }

  /**
   * Find wallets by currency
   * @param {string} currency - Cryptocurrency currency code
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByCurrency(currency, options = new QueryOptions()) {
    throw new Error('findByCurrency() method must be implemented');
  }

  /**
   * Find wallet by address
   * @param {string} address - Wallet address
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByAddress(address, options = new QueryOptions()) {
    throw new Error('findByAddress() method must be implemented');
  }

  /**
   * Find wallets with team information (joined data)
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findWithTeamInfo(options = new QueryOptions()) {
    throw new Error('findWithTeamInfo() method must be implemented');
  }

  /**
   * Get wallet statistics
   * @param {Object} options - Statistics options
   * @param {Date} options.since - Date threshold for recent data
   * @returns {Promise<QueryResult>}
   */
  async getStatistics(options = {}) {
    throw new Error('getStatistics() method must be implemented');
  }

  /**
   * Get wallets grouped by currency
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async getWalletsByCurrency(options = new QueryOptions()) {
    throw new Error('getWalletsByCurrency() method must be implemented');
  }

  /**
   * Count wallets by currency
   * @param {string} currency - Cryptocurrency currency code
   * @returns {Promise<QueryResult>}
   */
  async countByCurrency(currency) {
    throw new Error('countByCurrency() method must be implemented');
  }

  /**
   * Count teams with wallets
   * @returns {Promise<QueryResult>}
   */
  async countTeamsWithWallets() {
    throw new Error('countTeamsWithWallets() method must be implemented');
  }

  /**
   * Create multiple wallets for a team
   * @param {string} teamId - Team's public ID
   * @param {string} teamInternalId - Team's internal ID
   * @param {Array<{currency: string, address: string}>} wallets - Wallet data
   * @returns {Promise<QueryResult>}
   */
  async createWalletsForTeam(teamId, teamInternalId, wallets) {
    throw new Error('createWalletsForTeam() method must be implemented');
  }

  /**
   * Check if wallet address exists
   * @param {string} address - Wallet address
   * @returns {Promise<boolean>}
   */
  async addressExists(address) {
    const result = await this.count(new QueryOptions({ where: { address } }));
    return result.success && result.count > 0;
  }

  /**
   * Check if team has wallet for currency
   * @param {string} teamId - Team's public ID
   * @param {string} currency - Cryptocurrency currency code
   * @returns {Promise<boolean>}
   */
  async teamHasWalletForCurrency(teamId, currency) {
    const result = await this.count(new QueryOptions({ 
      where: { team_id: teamId, currency } 
    }));
    return result.success && result.count > 0;
  }

  /**
   * Get supported currencies
   * @returns {string[]}
   */
  getSupportedCurrencies() {
    return [
      'USDTTRC',
      'TRX',
      'SOL',
      'NOT',
      'XMR',
      'XRP',
      'DOGE',
    ];
  }

  /**
   * Validate wallet address for currency
   * @param {string} address - Wallet address
   * @param {string} currency - Cryptocurrency currency code
   * @returns {boolean}
   */
  validateAddressForCurrency(address, currency) {
    if (!address || typeof address !== 'string') return false;

    switch (currency) {
      case 'USDTTRC':
      case 'TRX':
        return /^T[A-Za-z0-9]{33}$/.test(address);
      case 'SOL':
        return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address);
      case 'XRP':
        return /^r[1-9A-HJ-NP-Za-km-z]{24,33}$/.test(address);
      case 'DOGE':
        return /^[DA][1-9A-HJ-NP-Za-km-z]{33}$/.test(address);
      default:
        return address.length >= 20 && address.length <= 100;
    }
  }

  /**
   * Delete wallets by team ID
   * @param {string} teamId - Team's public ID
   * @returns {Promise<QueryResult>}
   */
  async deleteByTeamId(teamId) {
    throw new Error('deleteByTeamId() method must be implemented');
  }

  /**
   * Find recently created wallets
   * @param {Date} since - Date threshold for recent creation
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findRecentlyCreated(since, options = new QueryOptions()) {
    throw new Error('findRecentlyCreated() method must be implemented');
  }

  /**
   * Get wallet creation report
   * @param {Date} since - Start date for report
   * @param {Date} until - End date for report
   * @returns {Promise<QueryResult>}
   */
  async getCreationReport(since, until = new Date()) {
    throw new Error('getCreationReport() method must be implemented');
  }

  /**
   * Find orphaned wallets (wallets without valid team)
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findOrphanedWallets(options = new QueryOptions()) {
    throw new Error('findOrphanedWallets() method must be implemented');
  }

  /**
   * Get table name for this repository
   * @returns {string}
   */
  getTableName() {
    return 'wallets';
  }
}
