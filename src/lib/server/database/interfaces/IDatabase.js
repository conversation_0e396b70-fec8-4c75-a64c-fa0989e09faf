/**
 * Core database interface for connection management and provider abstraction
 * This interface defines the contract that all database providers must implement
 */
export class IDatabase {
  /**
   * Initialize the database connection
   * @returns {Promise<void>}
   */
  async connect() {
    throw new Error('connect() method must be implemented');
  }

  /**
   * Close the database connection
   * @returns {Promise<void>}
   */
  async disconnect() {
    throw new Error('disconnect() method must be implemented');
  }

  /**
   * Check if the database connection is healthy
   * @returns {Promise<boolean>}
   */
  async isHealthy() {
    throw new Error('isHealthy() method must be implemented');
  }

  /**
   * Get the database provider name
   * @returns {string}
   */
  getProviderName() {
    throw new Error('getProviderName() method must be implemented');
  }

  /**
   * Begin a database transaction
   * @returns {Promise<ITransaction>}
   */
  async beginTransaction() {
    throw new Error('beginTransaction() method must be implemented');
  }

  /**
   * Execute a raw query (provider-specific)
   * @param {string} query - The query string
   * @param {any[]} params - Query parameters
   * @returns {Promise<any>}
   */
  async executeRaw(query, params = []) {
    throw new Error('executeRaw() method must be implemented');
  }
}

/**
 * Transaction interface for database operations
 */
export class ITransaction {
  /**
   * Commit the transaction
   * @returns {Promise<void>}
   */
  async commit() {
    throw new Error('commit() method must be implemented');
  }

  /**
   * Rollback the transaction
   * @returns {Promise<void>}
   */
  async rollback() {
    throw new Error('rollback() method must be implemented');
  }

  /**
   * Check if the transaction is active
   * @returns {boolean}
   */
  isActive() {
    throw new Error('isActive() method must be implemented');
  }
}

/**
 * Query result interface for standardized responses
 */
export class QueryResult {
  /**
   * @param {any[]} data - The result data
   * @param {number} count - Total count (for pagination)
   * @param {any} error - Error object if any
   * @param {any} metadata - Additional metadata
   */
  constructor(data = [], count = 0, error = null, metadata = {}) {
    this.data = data;
    this.count = count;
    this.error = error;
    this.metadata = metadata;
    this.success = !error;
  }

  /**
   * Create a successful result
   * @param {any[]} data 
   * @param {number} count 
   * @param {any} metadata 
   * @returns {QueryResult}
   */
  static success(data, count = null, metadata = {}) {
    return new QueryResult(data, count ?? data?.length ?? 0, null, metadata);
  }

  /**
   * Create an error result
   * @param {Error|string|Object} error
   * @param {any} metadata
   * @returns {QueryResult}
   */
  static error(error, metadata = {}) {
    let errorObj;
    if (error instanceof Error) {
      errorObj = error;
    } else if (typeof error === 'string') {
      errorObj = new Error(error);
    } else if (error && typeof error === 'object') {
      // Handle Supabase error objects
      const message = error.message || error.error_description || error.hint || JSON.stringify(error);
      errorObj = new Error(message);
      errorObj.code = error.code;
      errorObj.details = error.details;
      errorObj.hint = error.hint;
    } else {
      errorObj = new Error('Unknown error occurred');
    }
    return new QueryResult([], 0, errorObj, metadata);
  }
}

/**
 * Query options for filtering, sorting, and pagination
 */
export class QueryOptions {
  /**
   * @param {Object} options
   * @param {Object} options.where - Where conditions
   * @param {Object} options.orderBy - Order by fields
   * @param {number} options.limit - Limit results
   * @param {number} options.offset - Offset for pagination
   * @param {string[]} options.select - Fields to select
   * @param {string[]} options.include - Relations to include
   */
  constructor(options = {}) {
    this.where = options.where || {};
    this.orderBy = options.orderBy || {};
    this.limit = options.limit;
    this.offset = options.offset;
    this.select = options.select;
    this.include = options.include || [];
  }
}
