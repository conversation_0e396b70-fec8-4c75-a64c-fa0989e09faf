import { ITeamRepository } from '../../interfaces/ITeamRepository.js';
import { QueryResult, QueryOptions } from '../../interfaces/IDatabase.js';
import { Team, TeamStatistics } from '../../entities/Team.js';

/**
 * Supabase implementation of Team repository
 */
export class SupabaseTeamRepository extends ITeamRepository {
  /**
   * @param {import('@supabase/supabase-js').SupabaseClient} supabaseClient
   */
  constructor(supabaseClient) {
    super(null); // Pass null for now, we'll handle this differently
    this.supabase = supabaseClient;
  }

  /**
   * Find team by public ID
   * @param {string} teamId - Team's public ID
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByTeamId(teamId, options) {
    try {
      // Ensure options is properly initialized
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase.from('teams').select('*').eq('id', teamId);

      if (options.select) {
        query = this.supabase.from('teams').select(options.select.join(', ')).eq('id', teamId);
      }

      const { data, error } = await query.single();

      if (error) {
        if (error.code === 'PGRST116') {
          return QueryResult.success(null, 0);
        }
        return QueryResult.error(error);
      }

      const team = Team.fromDb(data);
      return QueryResult.success(team, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find teams with balance above threshold
   * @param {number} threshold - Minimum balance threshold
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findTeamsWithBalance(threshold = 0, options) {
    try {
      // Ensure options is properly initialized
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase.from('teams').select('*').gt('balance', threshold);

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const teams = data?.map(item => Team.fromDb(item)) || [];
      return QueryResult.success(teams, count || teams.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find teams with balance below threshold (low balance teams)
   * @param {number} threshold - Maximum balance threshold
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findLowBalanceTeams(threshold = 10, options) {
    try {
      // Ensure options is properly initialized
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase.from('teams').select('*').lt('balance', threshold);
      
      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const teams = data?.map(item => Team.fromDb(item)) || [];
      return QueryResult.success(teams, count || teams.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find top teams by balance
   * @param {number} limit - Number of teams to return
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findTopTeamsByBalance(limit = 10, options) {
    try {
      // Ensure options is properly initialized
      if (!options) {
        options = new QueryOptions();
      }

      const { data, error } = await this.supabase
        .from('teams')
        .select('*')
        .order('balance', { ascending: false })
        .limit(limit);

      if (error) {
        return QueryResult.error(error);
      }

      const teams = data?.map(item => Team.fromDb(item)) || [];
      return QueryResult.success(teams, teams.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find recently created teams
   * @param {Date} since - Date threshold for recent teams
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findRecentTeams(since, options) {
    try {
      // Ensure options is properly initialized
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase
        .from('teams')
        .select('*')
        .gte('created_at', since.toISOString());

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const teams = data?.map(item => Team.fromDb(item)) || [];
      return QueryResult.success(teams, count || teams.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Update team balance
   * @param {string} teamId - Team's public ID
   * @param {number} newBalance - New balance amount
   * @returns {Promise<QueryResult>}
   */
  async updateBalance(teamId, newBalance) {
    try {
      const { data, error } = await this.supabase
        .from('teams')
        .update({ balance: newBalance })
        .eq('id', teamId)
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const team = Team.fromDb(data);
      return QueryResult.success(team, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Update team balance with optimistic concurrency check
   * @param {string} teamId - Team's public ID
   * @param {number} newBalance - New balance amount
   * @param {number} expectedCurrentBalance - Expected current balance for concurrency check
   * @returns {Promise<QueryResult>}
   */
  async updateBalanceWithConcurrencyCheck(teamId, newBalance, expectedCurrentBalance) {
    try {
      const { data, error } = await this.supabase
        .from('teams')
        .update({ balance: newBalance })
        .eq('id', teamId)
        .eq('balance', expectedCurrentBalance) // optimistic concurrency check
        .select()
        .maybeSingle();

      if (error) {
        return QueryResult.error(error);
      }

      if (!data) {
        return QueryResult.error(new Error('Concurrent update detected - balance was modified by another process'));
      }

      const team = Team.fromDb(data);
      return QueryResult.success(team, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Get total balance across all teams
   * @returns {Promise<QueryResult>}
   */
  async getTotalBalance() {
    try {
      const { data, error } = await this.supabase
        .from('teams')
        .select('balance');

      if (error) {
        return QueryResult.error(error);
      }

      const totalBalance = data?.reduce((sum, team) => sum + (team.balance || 0), 0) || 0;
      return QueryResult.success({ totalBalance }, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Standard CRUD operations
   */
  async findById(id, options) {
    return this.findByTeamId(id, options);
  }

  async findMany(options) {
    try {
      // Ensure options is properly initialized
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase.from('teams').select('*', { count: 'exact' });

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 1000) - 1);
      }

      const { data, error, count } = await query;

      if (error) {
        console.error('Supabase findMany error:', error);
        return QueryResult.error(error);
      }

      const teams = data?.map(item => Team.fromDb(item)) || [];
      return QueryResult.success(teams, count || teams.length);
    } catch (error) {
      console.error('findMany catch error:', error);
      return QueryResult.error(error);
    }
  }

  async create(data) {
    try {
      const validation = Team.validate(data, 'create');
      if (!validation.valid) {
        return QueryResult.error(new Error(`Validation failed: ${validation.errors.join(', ')}`));
      }

      const { data: result, error } = await this.supabase
        .from('teams')
        .insert([data])
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const team = Team.fromDb(result);
      return QueryResult.success(team, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async updateById(id, data) {
    try {
      const validation = Team.validate(data, 'update');
      if (!validation.valid) {
        return QueryResult.error(new Error(`Validation failed: ${validation.errors.join(', ')}`));
      }

      const { data: result, error } = await this.supabase
        .from('teams')
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const team = Team.fromDb(result);
      return QueryResult.success(team, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async deleteById(id) {
    try {
      const { error } = await this.supabase
        .from('teams')
        .delete()
        .eq('id', id);

      if (error) {
        return QueryResult.error(error);
      }

      return QueryResult.success(null, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async count(options) {
    try {
      // Ensure options is properly initialized
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase.from('teams').select('*', { count: 'exact', head: true });

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { count, error } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      return QueryResult.success([], count || 0);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Increment team balance by amount
   * @param {string} teamId - Team's public ID
   * @param {number} amount - Amount to add (can be negative)
   * @returns {Promise<QueryResult>}
   */
  async incrementBalance(teamId, amount) {
    try {
      // First get current balance
      const currentResult = await this.findByTeamId(teamId);
      if (!currentResult.success || !currentResult.data) {
        return QueryResult.error(new Error('Team not found'));
      }

      const currentBalance = currentResult.data.balance;
      const newBalance = currentBalance + amount;

      return await this.updateBalance(teamId, newBalance);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Get team statistics
   * @param {Object} options - Statistics options
   * @returns {Promise<QueryResult>}
   */
  async getStatistics(options = {}) {
    try {
      const since = options.since || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
      const lowBalanceThreshold = options.lowBalanceThreshold || 10;
      const topTeamsLimit = options.topTeamsLimit || 5;

      const [
        totalResult,
        balanceResult,
        teamsWithBalanceResult,
        lowBalanceResult,
        topTeamsResult,
        recentResult
      ] = await Promise.all([
        this.count(),
        this.getTotalBalance(),
        this.findTeamsWithBalance(0),
        this.findLowBalanceTeams(lowBalanceThreshold),
        this.findTopTeamsByBalance(topTeamsLimit),
        this.findRecentTeams(since)
      ]);

      if (!totalResult.success || !balanceResult.success) {
        return QueryResult.error(new Error('Failed to fetch team statistics'));
      }

      const statistics = new TeamStatistics({
        totalTeams: totalResult.count,
        totalBalance: balanceResult.data.totalBalance,
        teamsWithBalance: teamsWithBalanceResult.success ? teamsWithBalanceResult.count : 0,
        lowBalanceTeams: lowBalanceResult.success ? lowBalanceResult.count : 0,
        topTeamsByBalance: topTeamsResult.success ? topTeamsResult.data : [],
        recentTeams: recentResult.success ? recentResult.count : 0
      });

      return QueryResult.success(statistics, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Delete team and all associated data
   * @param {string} teamId - Team's public ID
   * @returns {Promise<QueryResult>}
   */
  async deleteTeamWithAssociatedData(teamId) {
    try {
      // This would typically be done in a transaction
      // For now, we'll delete in sequence (wallets, transactions, devices, then team)

      const deleteOperations = await Promise.all([
        this.supabase.from('wallets').delete().eq('team_id', teamId),
        this.supabase.from('transactions').delete().eq('team_id', teamId),
        this.supabase.from('devices').delete().eq('team_id', teamId)
      ]);

      // Check for errors in associated data deletion
      for (const operation of deleteOperations) {
        if (operation.error) {
          return QueryResult.error(operation.error);
        }
      }

      // Finally delete the team
      const { error } = await this.supabase.from('teams').delete().eq('id', teamId);

      if (error) {
        return QueryResult.error(error);
      }

      return QueryResult.success(null, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find teams by owner
   * @param {string} ownerId - Owner's public ID
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByOwner(ownerId, options) {
    try {
      // Ensure options is properly initialized
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase.from('teams').select('*').eq('owner_id', ownerId);

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const teams = data?.map(item => Team.fromDb(item)) || [];
      return QueryResult.success(teams, count || teams.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Update next charge date
   * @param {string} teamId - Team's public ID
   * @param {Date} nextChargeAt - Next charge timestamp
   * @returns {Promise<QueryResult>}
   */
  async updateNextChargeAt(teamId, nextChargeAt) {
    try {
      const { data, error } = await this.supabase
        .from('teams')
        .update({ next_charge_at: nextChargeAt.toISOString() })
        .eq('id', teamId)
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const team = Team.fromDb(data);
      return QueryResult.success(team, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Update team charge amount
   * @param {string} teamId - Team's public ID
   * @param {number} chargeAmount - New charge amount
   * @returns {Promise<QueryResult>}
   */
  async updateChargeAmount(teamId, chargeAmount) {
    try {
      const { data, error } = await this.supabase
        .from('teams')
        .update({ charge_amount: chargeAmount })
        .eq('id', teamId)
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const team = Team.fromDb(data);
      return QueryResult.success(team, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find teams due for charging
   * @param {Date} beforeDate - Find teams with next_charge_at before this date
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findTeamsDueForCharging(beforeDate = new Date(), options) {
    try {
      // Ensure options is properly initialized
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase
        .from('teams')
        .select('*')
        .not('next_charge_at', 'is', null)
        .lte('next_charge_at', beforeDate.toISOString());

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const teams = data?.map(item => Team.fromDb(item)) || [];
      return QueryResult.success(teams, count || teams.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  // Additional required methods with basic implementations
  async findOne(options) {
    // Ensure options is properly initialized
    if (!options) {
      options = new QueryOptions();
    }

    const result = await this.findMany({ ...options, limit: 1 });
    if (result.success && result.data.length > 0) {
      return QueryResult.success(result.data[0], 1);
    }
    return QueryResult.success(null, 0);
  }

  async createMany(data) {
    try {
      const { data: result, error } = await this.supabase
        .from('teams')
        .insert(data)
        .select();

      if (error) {
        return QueryResult.error(error);
      }

      const teams = result?.map(item => Team.fromDb(item)) || [];
      return QueryResult.success(teams, teams.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async updateMany(options, data) {
    try {
      let query = this.supabase.from('teams').update(data);

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { data: result, error } = await query.select();

      if (error) {
        return QueryResult.error(error);
      }

      const teams = result?.map(item => Team.fromDb(item)) || [];
      return QueryResult.success(teams, teams.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async deleteMany(options) {
    try {
      let query = this.supabase.from('teams').delete();

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { error } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      return QueryResult.success(null, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async executeCustomQuery(queryName, params = {}) {
    throw new Error(`Custom query '${queryName}' not implemented`);
  }

  getTableName() {
    return 'teams';
  }

  // =========================================================================
  // BILLING-SPECIFIC METHODS FOR CRON JOBS
  // =========================================================================

  /**
   * Find teams with charges due in specified number of days
   * @param {number} daysFromNow - Number of days from today
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findTeamsWithUpcomingCharges(daysFromNow, options = new QueryOptions()) {
    try {
      // Create date in Europe/Kyiv timezone
      const now = new Date();
      const kyivOffset = 3 * 60 * 60 * 1000; // UTC+3 for Europe/Kyiv (adjust if needed for DST)
      const kyivTime = new Date(now.getTime() + kyivOffset);
      
      // Set to start of day in Kyiv time
      const targetDate = new Date(kyivTime);
      targetDate.setDate(targetDate.getDate() + daysFromNow);
      targetDate.setUTCHours(0, 0, 0, 0);
      
      // Set to end of day in Kyiv time
      const endDate = new Date(targetDate);
      endDate.setUTCHours(23, 59, 59, 999);

      let query = this.supabase
        .from('teams')
        .select('*')
        .not('next_charge_at', 'is', null)
        .gte('next_charge_at', targetDate.toISOString())
        .lte('next_charge_at', endDate.toISOString());

      if (options.limit) {
        query = query.limit(options.limit);
      }

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      }

      const { data, error, count } = await query;

      if (error) {
        console.error('[findTeamsWithUpcomingCharges] Database error:', error);
        return QueryResult.error(error);
      }

      const teams = data?.map(item => Team.fromDb(item)) || [];
      
      return QueryResult.success(teams, count || teams.length);
    } catch (error) {
      console.error('[findTeamsWithUpcomingCharges] Error:', error);
      return QueryResult.error(error);
    }
  }

  /**
   * Find teams for notification (alias for findTeamsWithUpcomingCharges)
   * @param {number} daysFromNow - Number of days from today
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findTeamsForNotification(daysFromNow, options = new QueryOptions()) {
    return this.findTeamsWithUpcomingCharges(daysFromNow, options);
  }

  /**
   * Process charge for a team with atomic transaction support
   * @param {string} teamId - Team's public ID
   * @param {number} amount - Amount to charge
   * @param {string} description - Transaction description
   * @returns {Promise<QueryResult>}
   */
  async processTeamCharge(teamId, amount, description = 'Service payment') {
    try {
      
      // First, get current team data with optimistic locking
      const teamResult = await this.findByTeamId(teamId);
      if (!teamResult.success || !teamResult.data) {
        return QueryResult.error(new Error('Team not found'));
      }

      const team = teamResult.data;
      const currentBalance = team.balance;

      // Check if sufficient balance
      if (currentBalance < amount) {
        console.error(`[processTeamCharge] Insufficient balance for team ${teamId}. Current: $${currentBalance}, Required: $${amount}`);
        return QueryResult.error(new Error('Insufficient balance'), {
          teamId,
          currentBalance,
          requiredAmount: amount,
          shortfall: amount - currentBalance,
          reason: 'insufficient_funds'
        });
      }

      // Calculate new balance
      const newBalance = currentBalance - amount;
      
      // Update balance with concurrency check
      const balanceUpdateResult = await this.updateBalanceWithConcurrencyCheck(
        teamId, 
        newBalance, 
        currentBalance
      );

      if (!balanceUpdateResult.success) {
        console.error(`[processTeamCharge] Failed to update balance for team ${teamId}:`, balanceUpdateResult.error);
        return QueryResult.error(balanceUpdateResult.error);
      }

      
      return QueryResult.success({
        teamId,
        chargedAmount: amount,
        previousBalance: currentBalance,
        newBalance,
        description,
        updatedTeam: balanceUpdateResult.data
      });

    } catch (error) {
      console.error(`[processTeamCharge] Error processing charge for team ${teamId}:`, error);
      return QueryResult.error(error);
    }
  }

  /**
   * Find teams with charges due today or overdue
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findTeamsDueToday(options = new QueryOptions()) {
    try {
      // Create date in Europe/Kyiv timezone
      const now = new Date();
      const kyivOffset = 3 * 60 * 60 * 1000; // UTC+3 for Europe/Kyiv (adjust if needed for DST)
      const kyivNow = new Date(now.getTime() + kyivOffset);
      
      // Set to start of day in Kyiv time
      const startOfDay = new Date(kyivNow);
      startOfDay.setUTCHours(0, 0, 0, 0);
      
      // Set to end of day in Kyiv time
      const endOfDay = new Date(kyivNow);
      endOfDay.setUTCHours(23, 59, 59, 999);
      
      // Get the current time in UTC
      const currentTime = new Date();
      
      let query = this.supabase
        .from('teams')
        .select('*')
        .not('next_charge_at', 'is', null)
        .lte('next_charge_at', endOfDay.toISOString())  // Include all dates up to end of today
        .or(`next_charge_at.gte.${startOfDay.toISOString()},next_charge_at.lt.${currentTime.toISOString()}`);  // Either today or in the past

      if (options.limit) {
        query = query.limit(options.limit);
      }

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      } else {
        // Default order by next_charge_at ascending (oldest first)
        query = query.order('next_charge_at', { ascending: true });
      }

      const { data, error, count } = await query;

      if (error) {
        console.error('[findTeamsDueToday] Database error:', error);
        return QueryResult.error(error);
      }

      const teams = data?.map(item => Team.fromDb(item)) || [];
      
      return QueryResult.success(teams, count || teams.length);
    } catch (error) {
      console.error('[findTeamsDueToday] Error:', error);
      return QueryResult.error(error);
    }
  }

  /**
   * Update team's next charge date and balance in a single operation
   * @param {string} teamId - Team's public ID
   * @param {number} newBalance - New balance
   * @param {Date} nextChargeAt - Next charge date
   * @returns {Promise<QueryResult>}
   */
  async updateBalanceAndNextCharge(teamId, newBalance, nextChargeAt) {
    try {
      const { data, error } = await this.supabase
        .from('teams')
        .update({ 
          balance: newBalance,
          next_charge_at: nextChargeAt.toISOString()
        })
        .eq('id', teamId)
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const team = Team.fromDb(data);
      return QueryResult.success(team, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Get billing statistics for monitoring
   * @returns {Promise<QueryResult>}
   */
  async getBillingStatistics() {
    try {
      const now = new Date();
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      const dayAfterTomorrow = new Date(now);
      dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);

      const [
        totalTeamsResult,
        dueNowResult,
        dueTomorrowResult,
        dueDayAfterResult,
        lowBalanceResult
      ] = await Promise.all([
        this.count(),
        this.findTeamsDueToday(),
        this.findTeamsWithUpcomingCharges(1),
        this.findTeamsWithUpcomingCharges(2),
        this.findLowBalanceTeams(10) // Teams with balance < $10
      ]);

      const statistics = {
        totalTeams: totalTeamsResult.success ? totalTeamsResult.count : 0,
        teamsDueNow: dueNowResult.success ? dueNowResult.count : 0,
        teamsDueTomorrow: dueTomorrowResult.success ? dueTomorrowResult.count : 0,
        teamsDueDayAfter: dueDayAfterResult.success ? dueDayAfterResult.count : 0,
        lowBalanceTeams: lowBalanceResult.success ? lowBalanceResult.count : 0,
        timestamp: now.toISOString()
      };

      return QueryResult.success(statistics, 1);
    } catch (error) {
      console.error('[getBillingStatistics] Error:', error);
      return QueryResult.error(error);
    }
  }

  /**
   * Get charge_amount statistics (total, average, min, max, top N teams)
   * @param {number} topN - Number of top teams to return
   * @returns {Promise<QueryResult>}
   */
  async getChargeAmountStatistics(topN = 5) {
    try {
      // Fetch all charge_amounts
      const { data, error } = await this.supabase
        .from('teams')
        .select('id, charge_amount')
        .order('charge_amount', { ascending: false });

      if (error) {
        return QueryResult.error(error);
      }
      const amounts = (data || []).map(t => Number(t.charge_amount) || 0);
      const total = amounts.reduce((a, b) => a + b, 0);
      const avg = amounts.length ? total / amounts.length : 0;
      const min = amounts.length ? Math.min(...amounts) : 0;
      const max = amounts.length ? Math.max(...amounts) : 0;
      const topTeams = (data || [])
        .sort((a, b) => (Number(b.charge_amount) || 0) - (Number(a.charge_amount) || 0))
        .slice(0, topN)
        .map(t => ({ id: t.id, charge_amount: Number(t.charge_amount) || 0 }));
      return QueryResult.success({ total, avg, min, max, topTeams }, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }
}
