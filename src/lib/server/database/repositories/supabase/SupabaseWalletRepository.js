import { IWalletRepository } from '../../interfaces/IWalletRepository.js';
import { QueryResult, QueryOptions } from '../../interfaces/IDatabase.js';
import { Wallet, WalletStatistics } from '../../entities/Wallet.js';

/**
 * Supabase implementation of Wallet repository
 */
export class SupabaseWalletRepository extends IWalletRepository {
  /**
   * @param {import('@supabase/supabase-js').SupabaseClient} supabaseClient
   */
  constructor(supabaseClient) {
    super(null);
    this.supabase = supabaseClient;
  }

  /**
   * Find wallets by team ID
   * @param {string} teamId - Team's public ID
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByTeamId(teamId, options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase.from('wallets').select('*').eq('team_id', teamId);

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const wallets = data?.map(item => Wallet.fromDb(item)) || [];
      return QueryResult.success(wallets, count || wallets.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find wallet by team ID and currency
   * @param {string} teamId - Team's public ID
   * @param {string} currency - Cryptocurrency currency code
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByTeamIdAndCurrency(teamId, currency, options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      const { data, error } = await this.supabase
        .from('wallets')
        .select('*')
        .eq('team_id', teamId)
        .eq('currency', currency)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return QueryResult.success(null, 0);
        }
        return QueryResult.error(error);
      }

      const wallet = Wallet.fromDb(data);
      return QueryResult.success(wallet, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find wallets by currency
   * @param {string} currency - Cryptocurrency currency code
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByCurrency(currency, options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase.from('wallets').select('*').eq('currency', currency);

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const wallets = data?.map(item => Wallet.fromDb(item)) || [];
      return QueryResult.success(wallets, count || wallets.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find wallet by address
   * @param {string} address - Wallet address
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByAddress(address, options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      const { data, error } = await this.supabase
        .from('wallets')
        .select('*')
        .eq('address', address)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return QueryResult.success(null, 0);
        }
        return QueryResult.error(error);
      }

      const wallet = Wallet.fromDb(data);
      return QueryResult.success(wallet, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find wallets with team information (joined data)
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findWithTeamInfo(options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase
        .from('wallets')
        .select(`
          *,
          teams:team_id (
            id,
            balance,
            owner_id,
            created_at
          )
        `);

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const wallets = data?.map(item => {
        const wallet = Wallet.fromDb(item);
        wallet.team = item.teams;
        return wallet;
      }) || [];

      return QueryResult.success(wallets, count || wallets.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Get wallet statistics
   * @param {Object} options - Statistics options
   * @returns {Promise<QueryResult>}
   */
  async getStatistics(options = {}) {
    try {
      const [
        totalResult,
        currencyGroupResult,
        teamsWithWalletsResult
      ] = await Promise.all([
        this.count(),
        this.getWalletsByCurrency(),
        this.countTeamsWithWallets()
      ]);

      if (!totalResult.success) {
        return QueryResult.error(new Error('Failed to fetch wallet statistics'));
      }

      const statistics = new WalletStatistics({
        totalWallets: totalResult.count,
        walletsByCurrency: currencyGroupResult.success ? currencyGroupResult.data : {},
        teamsWithWallets: teamsWithWalletsResult.success ? teamsWithWalletsResult.count : 0
      });

      return QueryResult.success(statistics, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Get wallets grouped by currency
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async getWalletsByCurrency(options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      const { data, error } = await this.supabase
        .from('wallets')
        .select('currency')
        .order('currency');

      if (error) {
        return QueryResult.error(error);
      }

      // Group by currency
      const currencyGroups = {};
      data?.forEach(item => {
        const currency = item.currency;
        currencyGroups[currency] = (currencyGroups[currency] || 0) + 1;
      });

      return QueryResult.success(currencyGroups, Object.keys(currencyGroups).length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Count wallets by currency
   * @param {string} currency - Cryptocurrency currency code
   * @returns {Promise<QueryResult>}
   */
  async countByCurrency(currency) {
    try {
      const { count, error } = await this.supabase
        .from('wallets')
        .select('*', { count: 'exact', head: true })
        .eq('currency', currency);

      if (error) {
        return QueryResult.error(error);
      }

      return QueryResult.success([], count || 0);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Count teams with wallets
   * @returns {Promise<QueryResult>}
   */
  async countTeamsWithWallets() {
    try {
      const { data, error } = await this.supabase
        .from('wallets')
        .select('team_id');

      if (error) {
        return QueryResult.error(error);
      }

      // Count unique team IDs
      const uniqueTeams = new Set(data?.map(item => item.team_id) || []);
      return QueryResult.success([], uniqueTeams.size);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Create multiple wallets for a team
   * @param {string} teamId - Team's public ID
   * @param {string} teamInternalId - Team's internal ID
   * @param {Array<{currency: string, address: string}>} wallets - Wallet data
   * @returns {Promise<QueryResult>}
   */
  async createWalletsForTeam(teamId, teamInternalId, wallets) {
    try {
      const walletData = wallets.map(wallet => ({
        team_id: teamId,
        team_internal_id: teamInternalId,
        currency: wallet.currency,
        address: wallet.address,
        created_at: new Date().toISOString()
      }));

      const { data, error } = await this.supabase
        .from('wallets')
        .insert(walletData)
        .select();

      if (error) {
        return QueryResult.error(error);
      }

      const createdWallets = data?.map(item => Wallet.fromDb(item)) || [];
      return QueryResult.success(createdWallets, createdWallets.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Standard CRUD operations
   */
  async findById(id, options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      const { data, error } = await this.supabase
        .from('wallets')
        .select('*')
        .eq('internal_id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return QueryResult.success(null, 0);
        }
        return QueryResult.error(error);
      }

      const wallet = Wallet.fromDb(data);
      return QueryResult.success(wallet, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async findMany(options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase.from('wallets').select('*', { count: 'exact' });

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 1000) - 1);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const wallets = data?.map(item => Wallet.fromDb(item)) || [];
      return QueryResult.success(wallets, count || wallets.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async create(data) {
    try {
      const validation = Wallet.validate(data, 'create');
      if (!validation.valid) {
        return QueryResult.error(new Error(`Validation failed: ${validation.errors.join(', ')}`));
      }

      const { data: result, error } = await this.supabase
        .from('wallets')
        .insert([data])
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const wallet = Wallet.fromDb(result);
      return QueryResult.success(wallet, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async updateById(id, data) {
    try {
      const validation = Wallet.validate(data, 'update');
      if (!validation.valid) {
        return QueryResult.error(new Error(`Validation failed: ${validation.errors.join(', ')}`));
      }

      const { data: result, error } = await this.supabase
        .from('wallets')
        .update(data)
        .eq('internal_id', id)
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const wallet = Wallet.fromDb(result);
      return QueryResult.success(wallet, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async deleteById(id) {
    try {
      const { error } = await this.supabase
        .from('wallets')
        .delete()
        .eq('internal_id', id);

      if (error) {
        return QueryResult.error(error);
      }

      return QueryResult.success(null, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async count(options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase.from('wallets').select('*', { count: 'exact', head: true });

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { count, error } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      return QueryResult.success([], count || 0);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  // Additional required methods
  async findOne(options) {
    if (!options) {
      options = new QueryOptions();
    }

    const result = await this.findMany({ ...options, limit: 1 });
    if (result.success && result.data.length > 0) {
      return QueryResult.success(result.data[0], 1);
    }
    return QueryResult.success(null, 0);
  }

  async createMany(data) {
    try {
      const { data: result, error } = await this.supabase
        .from('wallets')
        .insert(data)
        .select();

      if (error) {
        return QueryResult.error(error);
      }

      const wallets = result?.map(item => Wallet.fromDb(item)) || [];
      return QueryResult.success(wallets, wallets.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async updateMany(options, data) {
    try {
      let query = this.supabase.from('wallets').update(data);

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { data: result, error } = await query.select();

      if (error) {
        return QueryResult.error(error);
      }

      const wallets = result?.map(item => Wallet.fromDb(item)) || [];
      return QueryResult.success(wallets, wallets.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async deleteMany(options) {
    try {
      let query = this.supabase.from('wallets').delete();

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { error } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      return QueryResult.success(null, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async executeCustomQuery(queryName, params = {}) {
    throw new Error(`Custom query '${queryName}' not implemented`);
  }

  /**
   * Delete wallets by team ID
   * @param {string} teamId - Team's public ID
   * @returns {Promise<QueryResult>}
   */
  async deleteByTeamId(teamId) {
    try {
      const { error } = await this.supabase
        .from('wallets')
        .delete()
        .eq('team_id', teamId);

      if (error) {
        return QueryResult.error(error);
      }

      return QueryResult.success(null, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Get table name for this repository
   * @returns {string}
   */
  getTableName() {
    return 'wallets';
  }
}
