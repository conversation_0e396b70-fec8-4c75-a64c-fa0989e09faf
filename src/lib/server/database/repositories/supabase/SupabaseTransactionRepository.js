import { ITransactionRepository } from '../../interfaces/ITransactionRepository.js';
import { QueryResult, QueryOptions } from '../../interfaces/IDatabase.js';
import { Transaction, TransactionStatistics } from '../../entities/Transaction.js';

/**
 * Supabase implementation of Transaction repository
 */
export class SupabaseTransactionRepository extends ITransactionRepository {
  /**
   * @param {import('@supabase/supabase-js').SupabaseClient} supabaseClient
   */
  constructor(supabaseClient) {
    super(null);
    this.supabase = supabaseClient;
  }

  /**
   * Find transactions by team ID
   * @param {string} teamId - Team's public ID
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByTeamId(teamId, options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase.from('transactions').select('*').eq('team_id', teamId);

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const transactions = data?.map(item => Transaction.fromDb(item)) || [];
      return QueryResult.success(transactions, count || transactions.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find transactions within date range
   * @param {Date} since - Start date
   * @param {Date} until - End date
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByDateRange(since, until, options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase
        .from('transactions')
        .select('*')
        .gte('created_at', since.toISOString())
        .lte('created_at', until.toISOString());

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const transactions = data?.map(item => Transaction.fromDb(item)) || [];
      return QueryResult.success(transactions, count || transactions.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find recent transactions
   * @param {Date} since - Date threshold for recent transactions
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findRecent(since, options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase
        .from('transactions')
        .select('*')
        .gte('created_at', since.toISOString())
        .order('created_at', { ascending: false });

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const transactions = data?.map(item => Transaction.fromDb(item)) || [];
      return QueryResult.success(transactions, count || transactions.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find transactions by amount range
   * @param {number} minAmount - Minimum amount
   * @param {number} maxAmount - Maximum amount
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findByAmountRange(minAmount, maxAmount, options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase
        .from('transactions')
        .select('*')
        .gte('amount', minAmount)
        .lte('amount', maxAmount);

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const transactions = data?.map(item => Transaction.fromDb(item)) || [];
      return QueryResult.success(transactions, count || transactions.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find transactions with team information (joined data)
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findWithTeamInfo(options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase
        .from('transactions')
        .select(`
          *,
          teams:team_id (
            id,
            balance,
            owner_id,
            created_at
          )
        `);

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const transactions = data?.map(item => {
        const transaction = Transaction.fromDb(item);
        transaction.team = item.teams;
        return transaction;
      }) || [];

      return QueryResult.success(transactions, count || transactions.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Get transaction statistics
   * @param {Object} options - Statistics options
   * @returns {Promise<QueryResult>}
   */
  async getStatistics(options = {}) {
    try {
      const since = options.since || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
      const until = options.until || new Date();

      const [
        totalResult,
        volumeResult,
        recentResult,
        averageResult
      ] = await Promise.all([
        this.count(),
        this.getTotalVolume(since, until),
        this.findRecent(since, new QueryOptions({ limit: 10 })),
        this.getAverageAmount(since, until)
      ]);

      if (!totalResult.success) {
        return QueryResult.error(new Error('Failed to fetch transaction statistics'));
      }

      const statistics = new TransactionStatistics({
        totalTransactions: totalResult.count,
        totalVolume: volumeResult.success ? volumeResult.data.totalVolume : 0,
        recentTransactions: recentResult.success ? recentResult.count : 0,
        recentTransactionsList: recentResult.success ? recentResult.data : [],
        averageTransactionAmount: averageResult.success ? averageResult.data.average : 0
      });

      return QueryResult.success(statistics, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Get total transaction volume
   * @param {Date} since - Start date (optional)
   * @param {Date} until - End date (optional)
   * @returns {Promise<QueryResult>}
   */
  async getTotalVolume(since = null, until = null) {
    try {
      let query = this.supabase.from('transactions').select('amount');

      if (since) {
        query = query.gte('created_at', since.toISOString());
      }
      if (until) {
        query = query.lte('created_at', until.toISOString());
      }

      const { data, error } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const totalVolume = data?.reduce((sum, transaction) => {
        return sum + Math.abs(transaction.amount || 0);
      }, 0) || 0;

      return QueryResult.success({ totalVolume }, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Get average transaction amount
   * @param {Date} since - Start date (optional)
   * @param {Date} until - End date (optional)
   * @returns {Promise<QueryResult>}
   */
  async getAverageAmount(since = null, until = null) {
    try {
      let query = this.supabase.from('transactions').select('amount');

      if (since) {
        query = query.gte('created_at', since.toISOString());
      }
      if (until) {
        query = query.lte('created_at', until.toISOString());
      }

      const { data, error } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      if (!data || data.length === 0) {
        return QueryResult.success({ average: 0 }, 1);
      }

      const totalAmount = data.reduce((sum, transaction) => {
        return sum + Math.abs(transaction.amount || 0);
      }, 0);

      const average = totalAmount / data.length;
      return QueryResult.success({ average }, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Create transaction with balance validation
   * @param {Object} transactionData - Transaction data
   * @param {boolean} validateBalance - Whether to validate balance calculation
   * @returns {Promise<QueryResult>}
   */
  async createWithValidation(transactionData, validateBalance = true) {
    try {
      if (validateBalance) {
        const transaction = new Transaction(transactionData);
        if (!transaction.isBalanceValid()) {
          return QueryResult.error(new Error('Invalid balance calculation'));
        }
      }

      return await this.create(transactionData);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find credit transactions (positive amounts)
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findCredits(options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase
        .from('transactions')
        .select('*')
        .gt('amount', 0);

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const transactions = data?.map(item => Transaction.fromDb(item)) || [];
      return QueryResult.success(transactions, count || transactions.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Find debit transactions (negative amounts)
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findDebits(options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase
        .from('transactions')
        .select('*')
        .lt('amount', 0);

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const transactions = data?.map(item => Transaction.fromDb(item)) || [];
      return QueryResult.success(transactions, count || transactions.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Standard CRUD operations
   */
  async findById(id, options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      const { data, error } = await this.supabase
        .from('transactions')
        .select('*')
        .eq('internal_id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return QueryResult.success(null, 0);
        }
        return QueryResult.error(error);
      }

      const transaction = Transaction.fromDb(data);
      return QueryResult.success(transaction, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async findMany(options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase.from('transactions').select('*', { count: 'exact' });

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 1000) - 1);
      }

      const { data, error, count } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      const transactions = data?.map(item => Transaction.fromDb(item)) || [];
      return QueryResult.success(transactions, count || transactions.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async create(data) {
    try {
      const validation = Transaction.validate(data, 'create');
      if (!validation.valid) {
        return QueryResult.error(new Error(`Validation failed: ${validation.errors.join(', ')}`));
      }

      const { data: result, error } = await this.supabase
        .from('transactions')
        .insert([data])
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const transaction = Transaction.fromDb(result);
      return QueryResult.success(transaction, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async updateById(id, data) {
    try {
      const validation = Transaction.validate(data, 'update');
      if (!validation.valid) {
        return QueryResult.error(new Error(`Validation failed: ${validation.errors.join(', ')}`));
      }

      const { data: result, error } = await this.supabase
        .from('transactions')
        .update(data)
        .eq('internal_id', id)
        .select()
        .single();

      if (error) {
        return QueryResult.error(error);
      }

      const transaction = Transaction.fromDb(result);
      return QueryResult.success(transaction, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async deleteById(id) {
    try {
      const { error } = await this.supabase
        .from('transactions')
        .delete()
        .eq('internal_id', id);

      if (error) {
        return QueryResult.error(error);
      }

      return QueryResult.success(null, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async count(options) {
    try {
      if (!options) {
        options = new QueryOptions();
      }

      let query = this.supabase.from('transactions').select('*', { count: 'exact', head: true });

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { count, error } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      return QueryResult.success([], count || 0);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  // Additional required methods
  async findOne(options) {
    if (!options) {
      options = new QueryOptions();
    }

    const result = await this.findMany({ ...options, limit: 1 });
    if (result.success && result.data.length > 0) {
      return QueryResult.success(result.data[0], 1);
    }
    return QueryResult.success(null, 0);
  }

  async createMany(data) {
    try {
      const { data: result, error } = await this.supabase
        .from('transactions')
        .insert(data)
        .select();

      if (error) {
        return QueryResult.error(error);
      }

      const transactions = result?.map(item => Transaction.fromDb(item)) || [];
      return QueryResult.success(transactions, transactions.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async updateMany(options, data) {
    try {
      let query = this.supabase.from('transactions').update(data);

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { data: result, error } = await query.select();

      if (error) {
        return QueryResult.error(error);
      }

      const transactions = result?.map(item => Transaction.fromDb(item)) || [];
      return QueryResult.success(transactions, transactions.length);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async deleteMany(options) {
    try {
      let query = this.supabase.from('transactions').delete();

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { error } = await query;

      if (error) {
        return QueryResult.error(error);
      }

      return QueryResult.success(null, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  async executeCustomQuery(queryName, params = {}) {
    throw new Error(`Custom query '${queryName}' not implemented`);
  }

  /**
   * Check if transaction exists by internal ID
   * @param {number} internalId - Transaction internal ID
   * @returns {Promise<boolean>}
   */
  async existsByInternalId(internalId) {
    try {
      const { data, error } = await this.supabase
        .from('transactions')
        .select('internal_id')
        .eq('internal_id', internalId)
        .maybeSingle();

      if (error) {
        console.error('Error checking transaction existence:', error);
        return false;
      }

      return !!data;
    } catch (error) {
      console.error('Error checking transaction existence:', error);
      return false;
    }
  }

  /**
   * Delete transactions by team ID
   * @param {string} teamId - Team's public ID
   * @returns {Promise<QueryResult>}
   */
  async deleteByTeamId(teamId) {
    try {
      const { error } = await this.supabase
        .from('transactions')
        .delete()
        .eq('team_id', teamId);

      if (error) {
        return QueryResult.error(error);
      }

      return QueryResult.success(null, 1);
    } catch (error) {
      return QueryResult.error(error);
    }
  }

  /**
   * Get table name for this repository
   * @returns {string}
   */
  getTableName() {
    return 'transactions';
  }
}
