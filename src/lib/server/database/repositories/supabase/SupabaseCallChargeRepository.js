import { ICallChargeRepository } from '../../interfaces/ICallChargeRepository.js';
import { QueryResult, QueryOptions } from '../../interfaces/IDatabase.js';
import { CallCharge } from '../../entities/CallCharge.js';

/**
 * Supabase implementation of CallCharge repository
 */
export class SupabaseCallChargeRepository extends ICallChargeRepository {
  /**
   * @param {import('@supabase/supabase-js').SupabaseClient} supabaseClient
   */
  constructor(supabaseClient) {
    super(null);
    this.supabase = supabaseClient;
  }

  /**
   * Get table name
   * @returns {string}
   */
  getTableName() {
    return 'call_charges';
  }

  /**
   * Find unprocessed call charges
   * @param {number} limit - Maximum number of records to return
   * @returns {Promise<QueryResult>}
   */
  async findUnprocessed(limit = 100) {
    try {
      const { data, error } = await this.supabase
        .from('call_charges')
        .select('*')
        .eq('processed', false)
        .order('created_at', { ascending: true })
        .limit(limit);

      if (error) {
        console.error('[SupabaseCallChargeRepository] Error finding unprocessed charges:', error);
        return new QueryResult(false, null, error.message);
      }

      const callCharges = data.map(row => CallCharge.fromDbRow(row));
      return new QueryResult(true, callCharges);
    } catch (error) {
      console.error('[SupabaseCallChargeRepository] Error in findUnprocessed:', error);
      return new QueryResult(false, null, error.message);
    }
  }

  /**
   * Atomically process call charges (mark as processed and return the records)
   * This method uses sequential processing to prevent race conditions
   * @param {number} limit - Maximum number of records to process
   * @returns {Promise<QueryResult>} Result containing the processed call charges
   */
  async atomicProcessBatch(limit = 100) {
    const processingId = `proc_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    
    try {
      // Step 1: Get unprocessed charges in sequential order (oldest first)
      const { data: unprocessedCharges, error: selectError } = await this.supabase
        .from('call_charges')
        .select('*')
        .eq('processed', false)
        .order('created_at', { ascending: true })
        .limit(limit);

      if (selectError) {
        console.error(`[SupabaseCallChargeRepository:${processingId}] Error selecting unprocessed charges:`, selectError);
        return new QueryResult(false, null, selectError.message);
      }

      if (!unprocessedCharges || unprocessedCharges.length === 0) {
        return new QueryResult(true, []);
      }

      console.log(`[SupabaseCallChargeRepository:${processingId}] Found ${unprocessedCharges.length} unprocessed charges`);

      // Step 2: Process charges sequentially with delay to prevent race conditions
      const successfullyProcessed = [];
      const errors = [];

      for (let i = 0; i < unprocessedCharges.length; i++) {
        const charge = unprocessedCharges[i];
        
        try {
          // Try to atomically update this single charge
          const { data: updatedCharge, error: updateError } = await this.supabase
            .from('call_charges')
            .update({ processed: true })
            .eq('cdr_id', charge.cdr_id)
            .eq('processed', false) // Only update if still unprocessed
            .select()
            .single();

          if (updateError) {
            if (updateError.code === 'PGRST116') {
              // Record was already processed by another instance
              console.log(`[SupabaseCallChargeRepository:${processingId}] Charge ${charge.cdr_id} already processed by another instance`);
            } else {
              errors.push({ cdr_id: charge.cdr_id, error: updateError.message });
            }
          } else if (updatedCharge) {
            successfullyProcessed.push(CallCharge.fromDbRow(updatedCharge));
            console.log(`[SupabaseCallChargeRepository:${processingId}] Successfully processed charge ${charge.cdr_id}`);
          }

          // Add configurable delay between processing charges to prevent race conditions
          // Skip delay for the last item
          if (i < unprocessedCharges.length - 1) {
            const delayMs = parseInt(process.env.PROCESSING_DELAY_MS || '1000');
            console.log(`[SupabaseCallChargeRepository:${processingId}] Waiting ${delayMs}ms before processing next charge...`);
            await new Promise(resolve => setTimeout(resolve, delayMs));
          }

        } catch (chargeError) {
          console.error(`[SupabaseCallChargeRepository:${processingId}] Error processing charge ${charge.cdr_id}:`, chargeError);
          errors.push({ cdr_id: charge.cdr_id, error: chargeError.message });
          
          // Still add delay even on error to maintain consistent timing
          if (i < unprocessedCharges.length - 1) {
            const delayMs = parseInt(process.env.PROCESSING_DELAY_MS || '1000');
            await new Promise(resolve => setTimeout(resolve, delayMs));
          }
        }
      }

      if (errors.length > 0) {
        console.warn(`[SupabaseCallChargeRepository:${processingId}] Encountered ${errors.length} errors during processing:`, errors);
      }

      console.log(`[SupabaseCallChargeRepository:${processingId}] Successfully processed ${successfullyProcessed.length} charges`);
      return new QueryResult(true, successfullyProcessed);

    } catch (error) {
      console.error(`[SupabaseCallChargeRepository:${processingId}] Error in atomicProcessBatch:`, error);
      return new QueryResult(false, null, error.message);
    }
  }

  /**
   * Mark call charges as processed atomically
   * @param {string[]} cdrIds - Array of CDR IDs to mark as processed
   * @returns {Promise<QueryResult>}
   */
  async markAsProcessed(cdrIds) {
    try {
      if (!cdrIds || cdrIds.length === 0) {
        return new QueryResult(true, []);
      }

      const { data, error } = await this.supabase
        .from('call_charges')
        .update({ processed: true })
        .in('cdr_id', cdrIds)
        .select();

      if (error) {
        console.error('[SupabaseCallChargeRepository] Error marking as processed:', error);
        return new QueryResult(false, null, error.message);
      }

      const callCharges = data.map(row => CallCharge.fromDbRow(row));
      return new QueryResult(true, callCharges);
    } catch (error) {
      console.error('[SupabaseCallChargeRepository] Error in markAsProcessed:', error);
      return new QueryResult(false, null, error.message);
    }
  }

  /**
   * Find call charges by SIP user
   * @param {string} sipUser - SIP user identifier
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findBySipUser(sipUser, options = new QueryOptions()) {
    try {
      let query = this.supabase
        .from('call_charges')
        .select('*')
        .eq('sip_user', sipUser);

      // Apply ordering
      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      // Apply limit
      if (options.limit) {
        query = query.limit(options.limit);
      }

      // Apply offset
      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 100) - 1);
      }

      const { data, error } = await query;

      if (error) {
        console.error('[SupabaseCallChargeRepository] Error finding by SIP user:', error);
        return new QueryResult(false, null, error.message);
      }

      const callCharges = data.map(row => CallCharge.fromDbRow(row));
      return new QueryResult(true, callCharges);
    } catch (error) {
      console.error('[SupabaseCallChargeRepository] Error in findBySipUser:', error);
      return new QueryResult(false, null, error.message);
    }
  }

  /**
   * Get call charge statistics
   * @param {Date} since - Start date
   * @param {Date} until - End date
   * @returns {Promise<QueryResult>}
   */
  async getStatistics(since, until) {
    try {
      let query = this.supabase
        .from('call_charges')
        .select('processed, duration, charge, cost');

      if (since) {
        query = query.gte('created_at', since.toISOString());
      }

      if (until) {
        query = query.lte('created_at', until.toISOString());
      }

      const { data, error } = await query;

      if (error) {
        console.error('[SupabaseCallChargeRepository] Error getting statistics:', error);
        return new QueryResult(false, null, error.message);
      }

      const stats = {
        total: data.length,
        processed: data.filter(row => row.processed).length,
        unprocessed: data.filter(row => !row.processed).length,
        totalDuration: data.reduce((sum, row) => sum + (parseFloat(row.duration) || 0), 0),
        totalCharge: data.reduce((sum, row) => sum + (parseFloat(row.charge) || 0), 0),
        totalCost: data.reduce((sum, row) => sum + (parseFloat(row.cost) || 0), 0)
      };

      return new QueryResult(true, stats);
    } catch (error) {
      console.error('[SupabaseCallChargeRepository] Error in getStatistics:', error);
      return new QueryResult(false, null, error.message);
    }
  }

  /**
   * Find by ID (cdr_id)
   * @param {string} cdrId - CDR ID
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findById(cdrId, options = new QueryOptions()) {
    try {
      const { data, error } = await this.supabase
        .from('call_charges')
        .select('*')
        .eq('cdr_id', cdrId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return new QueryResult(true, null); // Not found
        }
        console.error('[SupabaseCallChargeRepository] Error finding by ID:', error);
        return new QueryResult(false, null, error.message);
      }

      const callCharge = CallCharge.fromDbRow(data);
      return new QueryResult(true, callCharge);
    } catch (error) {
      console.error('[SupabaseCallChargeRepository] Error in findById:', error);
      return new QueryResult(false, null, error.message);
    }
  }

  /**
   * Find multiple call charges
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async findMany(options = new QueryOptions()) {
    try {
      let query = this.supabase.from('call_charges').select('*');

      // Apply where conditions
      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      // Apply ordering
      if (options.orderBy && Object.keys(options.orderBy).length > 0) {
        const orderField = Object.keys(options.orderBy)[0];
        const orderDirection = options.orderBy[orderField] === 'desc' ? false : true;
        query = query.order(orderField, { ascending: orderDirection });
      } else {
        query = query.order('created_at', { ascending: false });
      }

      // Apply limit
      if (options.limit) {
        query = query.limit(options.limit);
      }

      // Apply offset
      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 100) - 1);
      }

      const { data, error } = await query;

      if (error) {
        console.error('[SupabaseCallChargeRepository] Error in findMany:', error);
        return new QueryResult(false, null, error.message);
      }

      const callCharges = data.map(row => CallCharge.fromDbRow(row));
      return new QueryResult(true, callCharges);
    } catch (error) {
      console.error('[SupabaseCallChargeRepository] Error in findMany:', error);
      return new QueryResult(false, null, error.message);
    }
  }

  /**
   * Create a new call charge
   * @param {Object} data - Call charge data
   * @returns {Promise<QueryResult>}
   */
  async create(data) {
    try {
      const validation = CallCharge.validate(data, 'create');
      if (!validation.valid) {
        return new QueryResult(false, null, `Validation failed: ${validation.errors.join(', ')}`);
      }

      const { data: result, error } = await this.supabase
        .from('call_charges')
        .insert(data)
        .select()
        .single();

      if (error) {
        console.error('[SupabaseCallChargeRepository] Error creating call charge:', error);
        return new QueryResult(false, null, error.message);
      }

      const callCharge = CallCharge.fromDbRow(result);
      return new QueryResult(true, callCharge);
    } catch (error) {
      console.error('[SupabaseCallChargeRepository] Error in create:', error);
      return new QueryResult(false, null, error.message);
    }
  }

  /**
   * Update call charge by ID
   * @param {string} cdrId - CDR ID
   * @param {Object} data - Update data
   * @returns {Promise<QueryResult>}
   */
  async updateById(cdrId, data) {
    try {
      const validation = CallCharge.validate(data, 'update');
      if (!validation.valid) {
        return new QueryResult(false, null, `Validation failed: ${validation.errors.join(', ')}`);
      }

      const { data: result, error } = await this.supabase
        .from('call_charges')
        .update(data)
        .eq('cdr_id', cdrId)
        .select()
        .single();

      if (error) {
        console.error('[SupabaseCallChargeRepository] Error updating call charge:', error);
        return new QueryResult(false, null, error.message);
      }

      const callCharge = CallCharge.fromDbRow(result);
      return new QueryResult(true, callCharge);
    } catch (error) {
      console.error('[SupabaseCallChargeRepository] Error in updateById:', error);
      return new QueryResult(false, null, error.message);
    }
  }

  /**
   * Count call charges
   * @param {QueryOptions} options - Query options
   * @returns {Promise<QueryResult>}
   */
  async count(options = new QueryOptions()) {
    try {
      let query = this.supabase
        .from('call_charges')
        .select('*', { count: 'exact', head: true });

      // Apply where conditions
      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { count, error } = await query;

      if (error) {
        console.error('[SupabaseCallChargeRepository] Error counting call charges:', error);
        return new QueryResult(false, null, error.message);
      }

      return new QueryResult(true, null, null, count);
    } catch (error) {
      console.error('[SupabaseCallChargeRepository] Error in count:', error);
      return new QueryResult(false, null, error.message);
    }
  }

  // Implement remaining abstract methods with basic functionality
  async findOne(options = new QueryOptions()) {
    const result = await this.findMany({ ...options, limit: 1 });
    if (result.success && result.data && result.data.length > 0) {
      return new QueryResult(true, result.data[0]);
    }
    return new QueryResult(true, null);
  }

  async createMany(data) {
    try {
      const { data: result, error } = await this.supabase
        .from('call_charges')
        .insert(data)
        .select();

      if (error) {
        console.error('[SupabaseCallChargeRepository] Error creating multiple call charges:', error);
        return new QueryResult(false, null, error.message);
      }

      const callCharges = result.map(row => CallCharge.fromDbRow(row));
      return new QueryResult(true, callCharges);
    } catch (error) {
      console.error('[SupabaseCallChargeRepository] Error in createMany:', error);
      return new QueryResult(false, null, error.message);
    }
  }

  async updateMany(options, data) {
    try {
      let query = this.supabase.from('call_charges').update(data);

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { data: result, error } = await query.select();

      if (error) {
        console.error('[SupabaseCallChargeRepository] Error updating multiple call charges:', error);
        return new QueryResult(false, null, error.message);
      }

      const callCharges = result.map(row => CallCharge.fromDbRow(row));
      return new QueryResult(true, callCharges);
    } catch (error) {
      console.error('[SupabaseCallChargeRepository] Error in updateMany:', error);
      return new QueryResult(false, null, error.message);
    }
  }

  async deleteById(cdrId) {
    try {
      const { error } = await this.supabase
        .from('call_charges')
        .delete()
        .eq('cdr_id', cdrId);

      if (error) {
        console.error('[SupabaseCallChargeRepository] Error deleting call charge:', error);
        return new QueryResult(false, null, error.message);
      }

      return new QueryResult(true, null);
    } catch (error) {
      console.error('[SupabaseCallChargeRepository] Error in deleteById:', error);
      return new QueryResult(false, null, error.message);
    }
  }

  async deleteMany(options) {
    try {
      let query = this.supabase.from('call_charges').delete();

      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
          query = query.eq(key, value);
        });
      }

      const { error } = await query;

      if (error) {
        console.error('[SupabaseCallChargeRepository] Error deleting multiple call charges:', error);
        return new QueryResult(false, null, error.message);
      }

      return new QueryResult(true, null);
    } catch (error) {
      console.error('[SupabaseCallChargeRepository] Error in deleteMany:', error);
      return new QueryResult(false, null, error.message);
    }
  }
}