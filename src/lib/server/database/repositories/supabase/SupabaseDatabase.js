import { IDatabase, ITransaction } from '../../interfaces/IDatabase.js';

/**
 * Supabase database implementation
 */
export class SupabaseDatabase extends IDatabase {
  /**
   * @param {import('@supabase/supabase-js').SupabaseClient} supabaseClient
   */
  constructor(supabaseClient) {
    super();
    this.client = supabaseClient;
    this.connected = false;
  }

  /**
   * Initialize the database connection
   * @returns {Promise<void>}
   */
  async connect() {
    try {
      // Test connection with a simple query
      const { error } = await this.client.from('teams').select('id').limit(1);
      if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows found" which is OK
        throw new Error(`Supabase connection failed: ${error.message}`);
      }
      this.connected = true;
    } catch (error) {
      this.connected = false;
      throw error;
    }
  }

  /**
   * Close the database connection
   * @returns {Promise<void>}
   */
  async disconnect() {
    // Supabase client doesn't need explicit disconnection
    this.connected = false;
  }

  /**
   * Check if the database connection is healthy
   * @returns {Promise<boolean>}
   */
  async isHealthy() {
    try {
      const { error } = await this.client.from('teams').select('id').limit(1);
      return !error || error.code === 'PGRST116';
    } catch (error) {
      return false;
    }
  }

  /**
   * Get the database provider name
   * @returns {string}
   */
  getProviderName() {
    return 'supabase';
  }

  /**
   * Begin a database transaction
   * @returns {Promise<ITransaction>}
   */
  async beginTransaction() {
    // Supabase doesn't support explicit transactions in the same way
    // We'll return a mock transaction for now
    return new SupabaseTransaction(this.client);
  }

  /**
   * Execute a raw query (provider-specific)
   * @param {string} query - The query string
   * @param {any[]} params - Query parameters
   * @returns {Promise<any>}
   */
  async executeRaw(query, params = []) {
    // Supabase doesn't support raw SQL queries through the client
    // This would need to be implemented using the REST API or RPC functions
    throw new Error('Raw queries not supported in Supabase client');
  }

  /**
   * Get the underlying Supabase client
   * @returns {import('@supabase/supabase-js').SupabaseClient}
   */
  getClient() {
    return this.client;
  }
}

/**
 * Supabase transaction implementation
 * Note: Supabase doesn't support explicit transactions through the client
 * This is a mock implementation for interface compatibility
 */
export class SupabaseTransaction extends ITransaction {
  /**
   * @param {import('@supabase/supabase-js').SupabaseClient} supabaseClient
   */
  constructor(supabaseClient) {
    super();
    this.client = supabaseClient;
    this.active = true;
    this.operations = [];
  }

  /**
   * Commit the transaction
   * @returns {Promise<void>}
   */
  async commit() {
    // In a real implementation, this would execute all queued operations
    // For now, we'll just mark as inactive
    this.active = false;
  }

  /**
   * Rollback the transaction
   * @returns {Promise<void>}
   */
  async rollback() {
    // In a real implementation, this would rollback all operations
    // For now, we'll just mark as inactive
    this.active = false;
  }

  /**
   * Check if the transaction is active
   * @returns {boolean}
   */
  isActive() {
    return this.active;
  }

  /**
   * Add an operation to the transaction
   * @param {Function} operation - Operation to add
   */
  addOperation(operation) {
    if (!this.active) {
      throw new Error('Transaction is not active');
    }
    this.operations.push(operation);
  }

  /**
   * Get the underlying Supabase client
   * @returns {import('@supabase/supabase-js').SupabaseClient}
   */
  getClient() {
    return this.client;
  }
}
