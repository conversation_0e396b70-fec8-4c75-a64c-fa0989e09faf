/**
 * CallCharge entity representing call charges in the system
 */
export class CallCharge {
  /**
   * @param {Object} data - CallCharge data
   * @param {string} data.cdr_id - CDR ID (primary key)
   * @param {string} data.sip_user - SIP user identifier
   * @param {number} data.duration - Call duration in seconds
   * @param {number} data.charge - Charge amount
   * @param {number} data.cost - Cost in USD
   * @param {boolean} data.processed - Whether the charge has been processed
   * @param {Date} data.start_time - Call start time
   * @param {Date} data.created_at - Record creation time
   */
  constructor(data = {}) {
    this.cdr_id = data.cdr_id || null;
    this.sip_user = data.sip_user || null;
    this.duration = data.duration || 0;
    this.charge = data.charge || 0;
    this.cost = data.cost || 0;
    this.processed = data.processed || false;
    this.start_time = data.start_time ? new Date(data.start_time) : null;
    this.created_at = data.created_at ? new Date(data.created_at) : new Date();
  }

  /**
   * Validate call charge data
   * @param {Object} data - Data to validate
   * @param {string} operation - Operation type (create, update)
   * @returns {{valid: boolean, errors: string[]}}
   */
  static validate(data, operation = 'create') {
    const errors = [];

    if (operation === 'create') {
      if (!data.cdr_id || typeof data.cdr_id !== 'string') {
        errors.push('cdr_id is required and must be a string');
      }

      if (!data.sip_user || typeof data.sip_user !== 'string') {
        errors.push('sip_user is required and must be a string');
      }

      if (typeof data.duration !== 'number' || data.duration < 0) {
        errors.push('duration must be a non-negative number');
      }

      if (typeof data.charge !== 'number' || data.charge < 0) {
        errors.push('charge must be a non-negative number');
      }

      if (!data.start_time) {
        errors.push('start_time is required');
      }
    }

    if (data.cost !== undefined && (typeof data.cost !== 'number' || data.cost < 0)) {
      errors.push('cost must be a non-negative number');
    }

    if (data.processed !== undefined && typeof data.processed !== 'boolean') {
      errors.push('processed must be a boolean');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Convert to summary object for API responses
   * @returns {Object}
   */
  toSummary() {
    return {
      cdr_id: this.cdr_id,
      sip_user: this.sip_user,
      duration: this.duration,
      charge: this.charge,
      cost: this.cost,
      processed: this.processed,
      start_time: this.start_time?.toISOString(),
      created_at: this.created_at?.toISOString()
    };
  }

  /**
   * Create CallCharge from database row
   * @param {Object} row - Database row
   * @returns {CallCharge}
   */
  static fromDbRow(row) {
    return new CallCharge({
      cdr_id: row.cdr_id,
      sip_user: row.sip_user,
      duration: parseFloat(row.duration) || 0,
      charge: parseFloat(row.charge) || 0,
      cost: parseFloat(row.cost) || 0,
      processed: Boolean(row.processed),
      start_time: row.start_time,
      created_at: row.created_at
    });
  }
}