/**
 * Transaction entity model
 * Represents a financial transaction with balance tracking
 */
export class Transaction {
  /**
   * @param {Object} data - Transaction data
   * @param {number} data.internal_id - Internal ID
   * @param {Date} data.created_at - Creation timestamp
   * @param {string} data.team_internal_id - Team's internal ID
   * @param {string} data.team_id - Team's public ID
   * @param {number} data.amount - Transaction amount (positive for credit, negative for debit)
   * @param {string} data.description - Transaction description
   * @param {number} data.balance_before - Balance before transaction
   * @param {number} data.balance_after - Balance after transaction
   */
  constructor(data = {}) {
    this.internal_id = data.internal_id;
    this.created_at = data.created_at ? new Date(data.created_at) : null;
    this.team_internal_id = data.team_internal_id;
    this.team_id = data.team_id;
    this.amount = Number(data.amount) || 0;
    this.description = data.description;
    this.balance_before = Number(data.balance_before) || 0;
    this.balance_after = Number(data.balance_after) || 0;
    
    // Related data (populated by joins)
    this.team = data.team || null;
  }

  /**
   * Transaction types
   */
  static TYPES = {
    DEPOSIT: 'deposit',
    WITHDRAWAL: 'withdrawal',
    CHARGE: 'charge',
    REFUND: 'refund',
    ADJUSTMENT: 'adjustment'
  };

  /**
   * Validate transaction data
   * @param {Partial<Transaction>} data - Transaction data to validate
   * @param {string} operation - Operation type (create, update)
   * @returns {{valid: boolean, errors: string[]}}
   */
  static validate(data, operation = 'create') {
    const errors = [];

    if (operation === 'create') {
      if (!data.team_internal_id || typeof data.team_internal_id !== 'string') {
        errors.push('Team internal ID is required and must be a string');
      }
      
      if (!data.team_id || typeof data.team_id !== 'string') {
        errors.push('Team ID is required and must be a string');
      }

      if (data.amount === undefined || typeof data.amount !== 'number') {
        errors.push('Amount is required and must be a number');
      }

      if (data.balance_before === undefined || typeof data.balance_before !== 'number') {
        errors.push('Balance before is required and must be a number');
      }

      if (data.balance_after === undefined || typeof data.balance_after !== 'number') {
        errors.push('Balance after is required and must be a number');
      }
    }

    if (data.amount !== undefined && typeof data.amount !== 'number') {
      errors.push('Amount must be a number');
    }

    if (data.balance_before !== undefined && (typeof data.balance_before !== 'number' || data.balance_before < 0)) {
      errors.push('Balance before must be a non-negative number');
    }

    if (data.balance_after !== undefined && (typeof data.balance_after !== 'number' || data.balance_after < 0)) {
      errors.push('Balance after must be a non-negative number');
    }

    if (data.description && typeof data.description !== 'string') {
      errors.push('Description must be a string');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate balance calculation
   * @returns {boolean}
   */
  isBalanceValid() {
    return Math.abs((this.balance_before + this.amount) - this.balance_after) < 0.01;
  }

  /**
   * Convert to database format
   * @returns {Object}
   */
  toDb() {
    return {
      internal_id: this.internal_id,
      created_at: this.created_at?.toISOString(),
      team_internal_id: this.team_internal_id,
      team_id: this.team_id,
      amount: this.amount,
      description: this.description,
      balance_before: this.balance_before,
      balance_after: this.balance_after
    };
  }

  /**
   * Create from database data
   * @param {Object} dbData - Raw database data
   * @returns {Transaction}
   */
  static fromDb(dbData) {
    return new Transaction(dbData);
  }

  /**
   * Get transaction summary for API responses
   * @returns {Object}
   */
  toSummary() {
    return {
      internal_id: this.internal_id,
      amount: this.amount,
      description: this.description,
      team_id: this.team_id,
      created_at: this.created_at?.toISOString(),
      type: this.getType()
    };
  }

  /**
   * Determine transaction type based on amount and description
   * @returns {string}
   */
  getType() {
    if (this.amount > 0) {
      if (this.description?.toLowerCase().includes('deposit')) {
        return Transaction.TYPES.DEPOSIT;
      }
      if (this.description?.toLowerCase().includes('refund')) {
        return Transaction.TYPES.REFUND;
      }
      return Transaction.TYPES.DEPOSIT;
    } else if (this.amount < 0) {
      if (this.description?.toLowerCase().includes('charge')) {
        return Transaction.TYPES.CHARGE;
      }
      if (this.description?.toLowerCase().includes('withdrawal')) {
        return Transaction.TYPES.WITHDRAWAL;
      }
      return Transaction.TYPES.CHARGE;
    }
    return Transaction.TYPES.ADJUSTMENT;
  }

  /**
   * Check if transaction is a credit (positive amount)
   * @returns {boolean}
   */
  isCredit() {
    return this.amount > 0;
  }

  /**
   * Check if transaction is a debit (negative amount)
   * @returns {boolean}
   */
  isDebit() {
    return this.amount < 0;
  }

  /**
   * Get absolute amount
   * @returns {number}
   */
  getAbsoluteAmount() {
    return Math.abs(this.amount);
  }

  /**
   * Format amount for display
   * @param {number} decimals - Number of decimal places
   * @returns {string}
   */
  formatAmount(decimals = 2) {
    return this.amount.toFixed(decimals);
  }
}

/**
 * Transaction creation data interface
 */
export class CreateTransactionData {
  /**
   * @param {Object} data
   * @param {string} data.team_internal_id - Team's internal ID
   * @param {string} data.team_id - Team's public ID
   * @param {number} data.amount - Transaction amount
   * @param {string} data.description - Transaction description
   * @param {number} data.balance_before - Balance before transaction
   * @param {number} data.balance_after - Balance after transaction
   * @param {number} data.internal_id - Optional internal ID for specific transactions
   */
  constructor(data) {
    this.team_internal_id = data.team_internal_id;
    this.team_id = data.team_id;
    this.amount = data.amount;
    this.description = data.description;
    this.balance_before = data.balance_before;
    this.balance_after = data.balance_after;
    this.created_at = new Date();
    
    // Allow setting specific internal_id for IPN transactions
    if (data.internal_id !== undefined) {
      this.internal_id = data.internal_id;
    }
  }
}

/**
 * Transaction statistics interface
 */
export class TransactionStatistics {
  /**
   * @param {Object} data
   * @param {number} data.totalTransactions - Total number of transactions
   * @param {number} data.totalVolume - Total transaction volume
   * @param {number} data.recentTransactions - Recent transactions count
   * @param {Transaction[]} data.recentTransactionsList - Recent transactions list
   * @param {Object} data.dailyTrends - Daily transaction trends
   * @param {number} data.averageTransactionAmount - Average transaction amount
   */
  constructor(data) {
    this.totalTransactions = data.totalTransactions || 0;
    this.totalVolume = data.totalVolume || 0;
    this.recentTransactions = data.recentTransactions || 0;
    this.recentTransactionsList = data.recentTransactionsList || [];
    this.dailyTrends = data.dailyTrends || {};
    this.averageTransactionAmount = data.averageTransactionAmount || 0;
  }
}
