import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import { env } from '$env/dynamic/private';

// Session configuration
const SESSION_SECRET = env.SESSION_SECRET;
const SESSION_EXPIRY = 60 * 60 * 24; // 24 hours in seconds

// File-based session storage configuration
// For svelte-adapter-bun, we need to ensure sessions are stored in the project root
// not in the build directory where the app actually runs
const getProjectRoot = () => {
  const cwd = process.cwd();
  // If we're running from build directory (production), go up one level
  if (cwd.endsWith('/build') || cwd.endsWith('\\build')) {
    return path.dirname(cwd);
  }
  return cwd;
};

const PROJECT_ROOT = getProjectRoot();
const SESSIONS_DIR = path.join(PROJECT_ROOT, '.sessions');
const SESSIONS_INDEX_FILE = path.join(SESSIONS_DIR, 'index.json');

// Ensure sessions directory exists
if (!fs.existsSync(SESSIONS_DIR)) {
  fs.mkdirSync(SESSIONS_DIR, { recursive: true });
}

// Initialize sessions index file if it doesn't exist
if (!fs.existsSync(SESSIONS_INDEX_FILE)) {
  fs.writeFileSync(SESSIONS_INDEX_FILE, JSON.stringify({}), 'utf8');
}

/**
 * Load sessions index from file
 * @returns {Object} Sessions index mapping sessionId to filename
 */
function loadSessionsIndex() {
  try {
    const data = fs.readFileSync(SESSIONS_INDEX_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error loading sessions index:', error);
    return {};
  }
}

/**
 * Save sessions index to file
 * @param {Object} index - Sessions index to save
 */
function saveSessionsIndex(index) {
  try {
    fs.writeFileSync(SESSIONS_INDEX_FILE, JSON.stringify(index, null, 2), 'utf8');
  } catch (error) {
    console.error('Error saving sessions index:', error);
  }
}

/**
 * Load session data from file
 * @param {string} sessionId - Session ID
 * @returns {Object|null} Session data or null if not found
 */
function loadSessionFromFile(sessionId) {
  try {
    const index = loadSessionsIndex();
    const filename = index[sessionId];
    
    if (!filename) {
      return null;
    }
    
    const sessionFile = path.join(SESSIONS_DIR, filename);
    
    if (!fs.existsSync(sessionFile)) {
      // Clean up orphaned index entry
      delete index[sessionId];
      saveSessionsIndex(index);
      return null;
    }
    
    const data = fs.readFileSync(sessionFile, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error loading session from file:', error);
    return null;
  }
}

/**
 * Save session data to file
 * @param {string} sessionId - Session ID
 * @param {Object} session - Session data to save
 */
function saveSessionToFile(sessionId, session) {
  try {
    const filename = `${sessionId}.json`;
    const sessionFile = path.join(SESSIONS_DIR, filename);
    
    // Save session data
    fs.writeFileSync(sessionFile, JSON.stringify(session, null, 2), 'utf8');
    
    // Update index
    const index = loadSessionsIndex();
    index[sessionId] = filename;
    saveSessionsIndex(index);
  } catch (error) {
    console.error('Error saving session to file:', error);
  }
}

/**
 * Delete session file
 * @param {string} sessionId - Session ID to delete
 */
function deleteSessionFile(sessionId) {
  try {
    const index = loadSessionsIndex();
    const filename = index[sessionId];
    
    if (filename) {
      const sessionFile = path.join(SESSIONS_DIR, filename);
      
      // Delete session file if it exists
      if (fs.existsSync(sessionFile)) {
        fs.unlinkSync(sessionFile);
      }
      
      // Remove from index
      delete index[sessionId];
      saveSessionsIndex(index);
    }
  } catch (error) {
    console.error('Error deleting session file:', error);
  }
}

/**
 * Create a new session
 * @param {Object} data - Data to store in the session
 * @returns {string} - Session ID
 */
export function createSession(data) {
  // Generate a secure random session ID
  const sessionId = crypto.randomBytes(32).toString('hex');

  // Create session with expiry
  const session = {
    id: sessionId,
    data,
    expires: Date.now() + SESSION_EXPIRY * 1000,
  };

  // Store session to file
  saveSessionToFile(sessionId, session);

  return sessionId;
}

/**
 * Get session data from session ID
 * @param {string} sessionId - Session ID
 * @returns {Object|null} - Session data or null if invalid/expired
 */
export function getSession(sessionId) {
  if (!sessionId) return null;

  // Handle development mode dummy session
  if (env.NODE_ENV === 'development' && sessionId === 'dev-session-id') {
    return { authenticated: true, user: 'dev-user' };
  }

  const session = loadSessionFromFile(sessionId);

  // Check if session exists and is not expired
  if (!session || session.expires < Date.now()) {
    if (session) {
      // Clean up expired session
      deleteSessionFile(sessionId);
    }
    return null;
  }

  return session.data;
}

/**
 * Delete a session
 * @param {string} sessionId - Session ID to delete
 */
export function deleteSession(sessionId) {
  if (sessionId) {
    deleteSessionFile(sessionId);
  }
}

/**
 * Create a signed session cookie value
 * @param {string} sessionId - Session ID
 * @returns {string} - Signed session value
 */
export function signSession(sessionId) {
  const hmac = crypto.createHmac('sha256', SESSION_SECRET);
  hmac.update(sessionId);
  const signature = hmac.digest('hex');
  return `${sessionId}.${signature}`;
}

/**
 * Verify and extract session ID from signed session value
 * @param {string|undefined} signedSession - Signed session value
 * @returns {string|null} - Session ID if valid, null otherwise
 */
export function verifySession(signedSession) {
  // Handle development mode dummy session
  if (env.NODE_ENV === 'development' && signedSession === 'dev-session-id') {
    return 'dev-session-id';
  }

  if (!signedSession || !signedSession.includes('.')) {
    return null;
  }

  const [sessionId, signature] = signedSession.split('.');

  const hmac = crypto.createHmac('sha256', SESSION_SECRET);
  hmac.update(sessionId);
  const expectedSignature = hmac.digest('hex');

  try {
    if (
      crypto.timingSafeEqual(
        Buffer.from(signature),
        Buffer.from(expectedSignature)
      )
    ) {
      return sessionId;
    }
  } catch (error) {
    return null;
  }

  return null;
}

/**
 * Clean up expired sessions (call periodically)
 */
export function cleanupSessions() {
  try {
    const now = Date.now();
    const index = loadSessionsIndex();
    
    // Check each session for expiration
    for (const sessionId of Object.keys(index)) {
      const session = loadSessionFromFile(sessionId);
      
      if (!session || session.expires < now) {
        // Delete expired session
        deleteSessionFile(sessionId);
      }
    }
  } catch (error) {
    console.error('Error during session cleanup:', error);
  }
}

// Set up periodic cleanup
if (typeof setInterval !== 'undefined') {
  // Only run in server environment
  setInterval(cleanupSessions, 60 * 60 * 1000); // Clean up every hour
}