import admin from 'firebase-admin';
import { getDeviceRepository } from '$lib/server/database/DatabaseFactory.js';
import { QueryOptions } from '$lib/server/database/interfaces/IDatabase.js';

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
 admin.initializeApp();
}

// -----------------------------------------------------------------------------
// Utility validation functions
// -----------------------------------------------------------------------------
/**
 * Simple UUID v4 validator (case-insensitive)
 * @param {string} str
 * @returns {boolean}
 */
export function isValidUUID(str) {
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
}

/**
 * Update FCM token and language for a device by IP address (customer_id from mobile app)
 * @param {string} ipAddress - IP address of the device (customer_id)
 * @param {string} fcmToken - FCM token to store
 * @param {string} [lang] - Device language preference
 * @returns {Promise<Object>} Result object
 */
export async function updateFcmTokenByIp(ipAddress, fcmToken, lang = null) {
  try {
    const deviceRepository = getDeviceRepository();

    // Find device by IP address
    const findResult = await deviceRepository.findByIP(ipAddress);

    if (!findResult.success || !findResult.data) {
      console.error(
        '[updateFcmTokenByIp] Device not found for IP:',
        ipAddress,
        findResult.error
      );
      throw new Error(`Device not found for IP address: ${ipAddress}`);
    }

    const device = findResult.data;

    // Update FCM token for the found device
    const updateTokenResult = await deviceRepository.updateFCMTokenByIP(ipAddress, fcmToken);

    if (!updateTokenResult.success) {
      console.error(
        '[updateFcmTokenByIp] Failed to update FCM token:',
        updateTokenResult.error
      );
      throw new Error('Failed to update FCM token');
    }

    // Update language if provided
    let updatedDevice = updateTokenResult.data;
    if (lang) {
      const updateLangResult = await deviceRepository.updateLanguageByIP(ipAddress, lang);
      
      if (!updateLangResult.success) {
        console.error(
          '[updateFcmTokenByIp] Failed to update language:',
          updateLangResult.error
        );
        // Don't throw error for language update failure, just log it
        console.warn('[updateFcmTokenByIp] Continuing without language update');
      } else {
        updatedDevice = updateLangResult.data;
      }
    }

    return {
      success: true,
      deviceId: device.internal_id,
      teamId: device.team_id,
      ip: device.ip,
      nickname: device.nickname,
      lang: updatedDevice?.lang || device.lang || null,
    };
  } catch (err) {
    console.error('[updateFcmTokenByIp] Error:', err);
    throw err;
  }
}

/**
 * Update FCM token for a device
 * @param {string} teamId - Team ID
 * @param {string} fcmToken - FCM token to store
 * @param {string} [deviceId] - Optional device identifier (IP, nickname, etc.)
 * @returns {Promise<Object>} Result object
 */
export async function updateFcmToken(teamId, fcmToken, deviceId = null) {
  try {
    const deviceRepository = getDeviceRepository();

    // If deviceId is provided, try to find specific device
    if (deviceId) {
      let deviceResult;

      // Try to find device by different identifiers
      if (isValidUUID(deviceId)) {
        // Try to find by internal_id first
        deviceResult = await deviceRepository.findById(deviceId);

        // Verify the device belongs to the correct team
        if (deviceResult.success && deviceResult.data && deviceResult.data.team_id !== teamId) {
          deviceResult = { success: false, data: null };
        }
      } else {
        // Try to find by IP first
        deviceResult = await deviceRepository.findByIP(deviceId);

        // Verify the device belongs to the correct team
        if (deviceResult.success && deviceResult.data && deviceResult.data.team_id !== teamId) {
          deviceResult = { success: false, data: null };
        }

        // If not found by IP, try to find by nickname within the team
        if (!deviceResult.success || !deviceResult.data) {
          deviceResult = await deviceRepository.findByNickname(deviceId, new QueryOptions({
            where: { team_id: teamId }
          }));

          // findByNickname returns an array, so get the first match
          if (deviceResult.success && deviceResult.data && Array.isArray(deviceResult.data)) {
            if (deviceResult.data.length > 0) {
              deviceResult.data = deviceResult.data[0];
            } else {
              deviceResult = { success: false, data: null };
            }
          }
        }
      }

      if (deviceResult.success && deviceResult.data) {
        // Update specific device
        const updateResult = await deviceRepository.updateFCMToken(deviceResult.data.internal_id, fcmToken);

        if (!updateResult.success) {
          console.error(
            '[updateFcmToken] Failed to update specific device:',
            updateResult.error
          );
          throw new Error('Failed to update FCM token for device');
        }

        return {
          success: true,
          deviceId: deviceResult.data.internal_id,
          updated: 'specific',
        };
      }
    }

    // If no specific device found or deviceId not provided, update the most recent device
    const devicesResult = await deviceRepository.findByTeamId(teamId, new QueryOptions({
      orderBy: { created_at: 'desc' },
      limit: 1
    }));

    if (!devicesResult.success) {
      console.error('[updateFcmToken] Failed to query devices:', devicesResult.error);
      throw new Error('Failed to query devices');
    }

    if (!devicesResult.data || devicesResult.data.length === 0) {
      throw new Error('No devices found for team');
    }

    const latestDevice = devicesResult.data[0];
    const updateResult = await deviceRepository.updateFCMToken(latestDevice.internal_id, fcmToken);

    if (!updateResult.success) {
      console.error(
        '[updateFcmToken] Failed to update latest device:',
        updateResult.error
      );
      throw new Error('Failed to update FCM token');
    }

    return {
      success: true,
      deviceId: latestDevice.internal_id,
      updated: 'latest',
    };
  } catch (err) {
    console.error('[updateFcmToken] Error:', err);
    throw err;
  }
}

/**
 * Get FCM tokens for a team
 * @param {string} teamId - Team ID
 * @param {string} [deviceId] - Optional specific device ID
 * @returns {Promise<string[]>} Array of FCM tokens
 */
export async function getFcmTokens(teamId, deviceId = null) {
  try {
    const deviceRepository = getDeviceRepository();
    let devicesResult;

    if (deviceId) {
      // Try to find specific device by different identifiers
      if (isValidUUID(deviceId)) {
        devicesResult = await deviceRepository.findById(deviceId);
      } else {
        // Try to find by IP first
        devicesResult = await deviceRepository.findByIP(deviceId);

        // If not found by IP, try to find by nickname
        if (!devicesResult.success || !devicesResult.data) {
          devicesResult = await deviceRepository.findByNickname(deviceId, new QueryOptions({
            where: { team_id: teamId }
          }));
        }
      }

      // Convert single device to array format
      if (devicesResult.success && devicesResult.data) {
        devicesResult.data = Array.isArray(devicesResult.data) ? devicesResult.data : [devicesResult.data];
      }
    } else {
      // Get all devices with FCM tokens for the team
      devicesResult = await deviceRepository.findWithFCMTokens(new QueryOptions({
        where: { team_id: teamId }
      }));
    }

    if (!devicesResult.success) {
      console.error('[getFcmTokens] Database error:', devicesResult.error);
      throw new Error('Failed to retrieve FCM tokens');
    }

    const devices = devicesResult.data || [];

    if (devices.length === 0) {
      throw new Error('No devices with FCM tokens found');
    }

    const tokens = devices
      .map((device) => device.fcm_token)
      .filter((token) => token && token.trim().length > 0);

    if (tokens.length === 0) {
      throw new Error('No valid FCM tokens found');
    }

    return tokens;
  } catch (err) {
    console.error('[getFcmTokens] Error:', err);
    throw err;
  }
}

/**
 * Send notification to devices
 * @param {Object} options - Notification options
 * @param {string} options.title - Notification title
 * @param {string} options.message - Notification message
 * @param {string} options.teamId - Team ID to send to (required)
 * @param {string} [options.deviceId] - Specific device ID to send to
 * @param {Object} [options.data] - Additional data payload
 * @returns {Promise<Object>} Result object
 */
export async function sendNotification({
  title,
  message,
  teamId,
  deviceId,
  data,
}) {
  if (!title || !message) {
    throw new Error('Missing required fields');
  }

  if (!teamId) {
    throw new Error('Team ID is required');
  }

  try {
    // Get tokens for specific team/device
    const tokens = await getFcmTokens(teamId, deviceId);

    if (tokens.length === 0) {
      throw new Error('No FCM tokens available for the specified team/device');
    }

    const results = [];
    const errors = [];

    // Send to each token
    for (const token of tokens) {
      const payload = {
        notification: {
          title,
          body: message,
        },
        token: token,
      };

      // Add data payload if provided
      if (data && typeof data === 'object') {
        payload.data = {};
        // Convert all data values to strings (FCM requirement)
        for (const [key, value] of Object.entries(data)) {
          payload.data[key] = String(value);
        }
      }

      if (process.env.NODE_ENV === 'development') {
        // In development, just log the notification
        results.push({
          token: token.substring(0, 20) + '...',
          response: { status: 'mocked_in_development' },
          success: true,
        });
      } else {
        // In production/staging, send actual notification
        try {
          const response = await admin.messaging().send(payload);
          results.push({
            token: token.substring(0, 20) + '...',
            response,
            success: true,
          });
        } catch (error) {
          console.error(
            `[sendNotification] Failed to send to token ${token.substring(0, 20)}...`,
            error
          );
          errors.push({
            token: token.substring(0, 20) + '...',
            error: error.message,
          });

          // If token is invalid, we might want to remove it from database
          if (
            error.code === 'messaging/registration-token-not-registered' ||
            error.code === 'messaging/invalid-registration-token'
          ) {
            try {
              const deviceRepository = getDeviceRepository();
              // Find devices with this token and clear it
              const devicesResult = await deviceRepository.findMany(new QueryOptions({
                where: { fcm_token: token }
              }));

              if (devicesResult.success && devicesResult.data.length > 0) {
                for (const device of devicesResult.data) {
                  await deviceRepository.updateFCMToken(device.internal_id, null);
                }
              }
            } catch (dbError) {
              console.error(
                '[sendNotification] Failed to remove invalid token:',
                dbError
              );
            }
          }
        }
      }
    }

    const successCount = results.length;
    const errorCount = errors.length;

    if (successCount === 0) {
      throw new Error(`Failed to send to all ${tokens.length} devices`);
    }

    return {
      success: true,
      results,
      errors,
      summary: {
        total: tokens.length,
        successful: successCount,
        failed: errorCount,
      },
    };
  } catch (error) {
    console.error('[sendNotification] Error:', error);
    throw error;
  }
}
