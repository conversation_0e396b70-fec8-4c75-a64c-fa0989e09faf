import { formatDistanceToNow, parseISO, isValid } from 'date-fns';

/**
 * Parse a date from various formats including string, number, or Date object
 * @param {string|number|Date} dateInput - The date to parse
 * @returns {Date|null} Parsed date or null if invalid
 */
function parseDate(dateInput) {
  if (!dateInput) return null;

  // If it's already a Date object and valid
  if (dateInput instanceof Date && isValid(dateInput)) {
    return dateInput;
  }

  // Handle string dates (most common case from backend)
  if (typeof dateInput === 'string') {
    // Try ISO 8601 format first (this is what your backend sends)
    const isoDate = parseISO(dateInput);
    if (isValid(isoDate)) return isoDate;

    // Fallback to native Date parsing
    const parsedDate = new Date(dateInput);
    if (isValid(parsedDate)) return parsedDate;
  }

  // Handle numeric timestamp (seconds or milliseconds)
  if (typeof dateInput === 'number' || /^\d+$/.test(dateInput)) {
    const num = Number(dateInput);
    // Assume seconds if < 1e12 (approx 2001), milliseconds otherwise
    const timestamp = num < 1e12 ? num * 1000 : num;
    const date = new Date(timestamp);
    return isValid(date) ? date : null;
  }

  return null;
}

/**
 * Format a date to show relative time (e.g., "2 hours ago", "3 days ago")
 * @param {string|number|Date|null|undefined} dateInput - The date to format
 * @param {string} [fallback='-'] - The fallback text when date is invalid
 * @returns {string} Formatted relative time string
 */
export function timeAgo(dateInput, fallback = '-') {
  if (!dateInput) return fallback;

  try {
    const date = parseDate(dateInput);
    if (!date || !isValid(date)) return fallback;

    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = diffMs / (1000 * 60); // Difference in minutes, can be negative for future dates

    // Handle future dates explicitly
    if (diffMs < 0) {
      // For dates in the future, use "in" prefix for clarity
      return formatDistanceToNow(date, {
        addSuffix: true,
        includeSeconds: false,
      }).replace('about ', ''); // Remove "about" for cleaner output
    }

    // Handle small differences (less than 2 minutes) as "just now"
    if (Math.abs(diffMinutes) < 2) {
      return 'just now';
    }

    // Handle past dates
    return formatDistanceToNow(date, {
      addSuffix: true,
      includeSeconds: false,
    }).replace('about ', ''); // Remove "about" for cleaner output
  } catch (error) {
    console.warn('Error formatting date:', error, 'Input:', dateInput);
    return fallback;
  }
}