import { minify } from 'html-minifier';
import { initializeDatabaseFactory } from '$lib/server/database/DatabaseFactory.js';
import { Elysia } from 'elysia';
import { 
  initializeBillingCronJobs, 
  getBillingCronControls 
} from '$lib/server/cron/index.js';

// Import VPN components to set up global handlers
import { ClientDatabase } from '$lib/../routes/api/[...slugs]/lib/vpn/database.js';
import { VpnHandlers } from '$lib/../routes/api/[...slugs]/lib/vpn/handlers.js';
import { setGlobalVpnHandlers } from '$lib/server/cron/utils/billingHelpers.js';

const minifyOpts = {
  collapseBooleanAttributes: true,
  collapseWhitespace: true,
  conservativeCollapse: true,
  decodeEntities: true,
  html5: true,
  ignoreCustomComments: [/^#/],
  minifyCSS: true,
  minifyJS: false,
  removeAttributeQuotes: true,
  removeComments: false,
  removeOptionalTags: true,
  removeRedundantAttributes: true,
  removeScriptTypeAttributes: true,
  removeStyleLinkTypeAttributes: true,
  sortAttributes: true,
  sortClassName: true,
};

const NOT_FOUND_RESPONSE = new Response('', { status: 404 });

// SvelteKit init function - runs once when server starts
export const init = async () => {
  try {
    // Use existing database initialization function
    await ensureDatabaseInitialized();

    // Initialize VPN handlers for billing system
    const vpnDb = ClientDatabase.getInstance();
    const vpnHandlers = new VpnHandlers(vpnDb);
    setGlobalVpnHandlers(vpnHandlers);

    // Initialize billing cron jobs
    let cronApp = new Elysia({ name: 'billing-cron-sveltekit' });
    cronApp = initializeBillingCronJobs(cronApp);

    // Get cron controls and run all jobs once at startup
    //const cronControls = getBillingCronControls(cronApp);
    //cronControls.runAllJobs();

  } catch (error) {
    console.error('[SvelteKit Init] ERROR: Failed to initialize services:', error);
    // Don't throw here to prevent server startup failure
  }
};

let databaseInitialized = false;
async function ensureDatabaseInitialized() {
  if (databaseInitialized) {
    return;
  }

  try {
    await initializeDatabaseFactory();
    databaseInitialized = true;
  } catch (error) {
    console.error('Failed to initialize database factory:', error);
    // Don't throw here, let individual requests handle the error
  }
}

let handleFunc = async ({ event, resolve }) => {
  try {
    if (!databaseInitialized) {
      await ensureDatabaseInitialized();
    }
    return await resolve(event, {
      transformPageChunk: ({ html, done }) => {
        return minify(html, minifyOpts);
      },
    });
  } catch {
    return NOT_FOUND_RESPONSE;
  }
};

if (process.env.NODE_ENV === 'development') {
  handleFunc = async ({ event, resolve }) => {
    try {
      if (!databaseInitialized) {
        await ensureDatabaseInitialized();
      }
      return await resolve(event);
    } catch (e) {
      throw e;
    }
  }
}

export const handle = handleFunc;

let handleErrorFunc = ({ error, event }) => {
  return NOT_FOUND_RESPONSE;
};

if (process.env.NODE_ENV === 'development') {
  handleErrorFunc = ({ error, event }) => {
    if (error instanceof Error && error.message.includes('favicon.ico')) {
      return NOT_FOUND_RESPONSE;
    }
    console.error('Unhandled error:', error, 'Event:', event);
    return {
      message: 'Internal Error',
      code: error?.code || 'UNKNOWN'
    };
  };
}

export const handleError = handleErrorFunc;