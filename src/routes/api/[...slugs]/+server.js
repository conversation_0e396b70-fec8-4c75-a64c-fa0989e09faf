// src/routes/api/[...slugs]/+server.js
import { Elysia } from 'elysia';
import { getClientIP } from './lib/helpers.js';

// Import domain modules
import { ClientDatabase } from './lib/vpn/database.js';
import { VpnHandlers } from './lib/vpn/handlers.js';
import { NotificationsHandlers } from './lib/notifications/handlers.js';
import { configureVpnRoutes } from './lib/vpn/routes.js';
import { configureNotificationsRoutes } from './lib/notifications/routes.js';

// Get singleton database instance
const db = ClientDatabase.getInstance();

// Create handlers
const vpnHandlers = new VpnHandlers(db);
const notificationsHandlers = new NotificationsHandlers();

// Configure the app with routes
let app = new Elysia({ prefix: '/api' });

// Add utility endpoint
app = app.get('/ip', ({ request }) => {
  return getClientIP(request);
});

// Configure domain routes
app = configureVpnRoutes(app, vpnHandlers);
app = configureNotificationsRoutes(app, notificationsHandlers);

/**
 * Handle GET requests
 * @param {Object} params - The request parameters
 * @param {Request} params.request - The HTTP request
 * @returns {Response} The HTTP response
 */
export const GET = ({ request }) => app.handle(request);

/**
 * Handle POST requests
 * @param {Object} params - The request parameters
 * @param {Request} params.request - The HTTP request
 * @returns {Response} The HTTP response
 */
export const POST = ({ request }) => app.handle(request);
