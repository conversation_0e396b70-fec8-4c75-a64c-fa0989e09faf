// src/routes/api/[...slugs]/lib/notifications/handlers.js
import { updateFcmTokenByIp, sendNotification } from '$lib/server/notifications.js';
import { convertToUSD } from '$lib/server/currencyService.js';
import { getWalletRepository, getTeamRepository, getTransactionRepository, getDeviceRepository } from '$lib/server/database/DatabaseFactory.js';
import { getPaymentReceivedNotification } from '$lib/server/cron/utils/notificationTemplates.js';
import { getClientIP } from '../helpers.js';

// WestWallet IPN source IP for security validation
const WESTWALLET_IPN_IP = '***********';
const WEBVIEW_AUTH_TOKEN = process.env.WEBVIEW_AUTH_TOKEN;

/**
 * Notifications-related API handlers
 */
export class NotificationsHandlers {
  /**
   * Handle FCM token update from Android app
   * @param {Object} body - Request body
   * @param {string} body.token - FCM token
   * @param {string} body.customer_id - Customer ID (IP address)
   * @param {string} [body.lang] - Device language preference
   * @param {Object} headers - Request headers
   * @param {string} [headers.pass] - Authentication token
   * @returns {Promise<Object>} Response object
   */
  async updateFcmToken(body, headers) {
    // Skip authentication in development mode
    if (process.env.NODE_ENV !== 'development') {
      const pass = headers.pass;
      if (pass !== WEBVIEW_AUTH_TOKEN) {
        console.warn('[FCM Token Update] Unauthorized access attempt');
        throw new Error('Unauthorized');
      }
    }

    const { token, customer_id: customerId, lang } = body;

    if (!token) {
      console.error('[FCM Token Update] Missing token in request body');
      throw new Error('Missing token');
    }

    if (!customerId) {
      console.error('[FCM Token Update] Missing customer_id in request body');
      throw new Error('Missing customer_id');
    }

    // Store token and language in database using IP address to find the device
    try {
      const result = await updateFcmTokenByIp(customerId, token, lang);

      return {
        success: true,
        message: 'FCM token and language updated successfully',
        deviceId: result.deviceId,
        teamId: result.teamId,
        lang: result.lang,
      };
    } catch (err) {
      console.error('[FCM Token Update] Failed to update FCM token and language:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      throw new Error(`Failed to update FCM token and language: ${errorMessage}`);
    }
  }

  /**
   * Validate that the request comes from WestWallet's authorized IP
   * @param {Request} request - The incoming request
   * @returns {boolean} True if request is from authorized IP
   */
  validateIpnSource(request) {
    const clientIp = getClientIP(request);

    // In development, we might want to skip IP validation
    if (process.env.NODE_ENV === 'development') {
      return true;
    }

    return clientIp === WESTWALLET_IPN_IP;
  }

  /**
   * Parse IPN data from request
   * @param {Request} request - The incoming request
   * @returns {Promise<Object>} Parsed data
   */
  async parseIpnData(request) {
    const contentType = request.headers.get('content-type');

    if (contentType?.includes('application/x-www-form-urlencoded')) {
      // Parse form-encoded data
      const formData = await request.formData();
      const data = {};
      for (const [key, value] of formData.entries()) {
        data[key] = value;
      }
      return data;
    } else if (contentType?.includes('application/json')) {
      // Parse JSON data (fallback)
      return await request.json();
    } else {
      throw new Error('Unsupported content type');
    }
  }

  /**
   * Find wallet and team information by address
   * @param {string} address - Wallet address
   * @returns {Promise<Object|null>} Wallet and team data or null
   */
  async findWalletByAddress(address) {
    try {
      const walletRepository = getWalletRepository();
      const walletResult = await walletRepository.findByAddress(address);

      if (!walletResult.success || !walletResult.data) {
        console.error('[IPN] Error finding wallet by address:', walletResult.error);
        return null;
      }

      return walletResult.data.toSummary ? walletResult.data.toSummary() : walletResult.data;
    } catch (error) {
      console.error('[IPN] Error finding wallet by address:', error);
      return null;
    }
  }

  /**
   * Handle IPN (Instant Payment Notification) webhook from WestWallet
   * @param {Request} request - The incoming request
   * @returns {Promise<Object>} Response object
   */
  async handleIpnWebhook(request) {
    const startTime = Date.now();
    const requestId = Math.random().toString(36).substring(2, 15);
    

    try {

    // Validate source IP
    const clientIp = getClientIP(request);
    
    if (!this.validateIpnSource(request)) {
      console.warn(`[IPN:${requestId}] Unauthorized IP address: ${clientIp}`);
      throw new Error('Unauthorized IP address');
    }

    // Parse IPN data
    const ipnData = await this.parseIpnData(request);

    // Validate required fields first
    const { id, amount, address, currency, status, blockchain_hash, label } = ipnData;
    
    if (!id || !amount || !address || !currency || !status) {
      console.error(`[IPN:${requestId}] Missing required fields - id: ${!!id}, amount: ${!!amount}, address: ${!!address}, currency: ${!!currency}, status: ${!!status}`);
      throw new Error('Missing required fields');
    }

    const txInternalId = Number(id);
    if (Number.isNaN(txInternalId) || txInternalId <= 0) {
      console.error(`[IPN:${requestId}] Invalid transaction id: ${id} -> ${txInternalId}`);
      throw new Error('Invalid transaction id');
    }

    // Only process notifications with status "completed"; ignore others like "pending"
    if (status !== 'completed') {
      return {
        success: true,
        message: `Notification with status ${status} ignored`,
      };
    }

    // Find the wallet and associated team
    const walletData = await this.findWalletByAddress(address);
    
    if (!walletData) {
      console.error(`[IPN:${requestId}] Wallet not found for address: ${address}`);
      throw new Error('Wallet not found');
    }

    const teamId = walletData.team_id;
    const teamInternalId = walletData.team_internal_id;

    // Validate team IDs
    if (!teamId || typeof teamId !== 'string') {
      console.error(`[IPN:${requestId}] Invalid team_id from wallet data: ${teamId} (type: ${typeof teamId})`);
      throw new Error('Invalid team ID from wallet data');
    }

    if (!teamInternalId || typeof teamInternalId !== 'string') {
      console.error(`[IPN:${requestId}] Invalid team_internal_id from wallet data: ${teamInternalId} (type: ${typeof teamInternalId})`);
      throw new Error('Invalid team internal ID from wallet data');
    }


    // Get current team balance and convert crypto amount to USD
    const teamRepository = getTeamRepository();
    const teamResult = await teamRepository.findByTeamId(teamId);

    if (!teamResult.success || !teamResult.data) {
      console.error(`[IPN:${requestId}] Error fetching team data:`, teamResult.error);
      throw new Error('Team not found');
    }

    const teamData = teamResult.data;
    const currentBalance = parseFloat(teamData.balance) || 0;

    // Validate incoming amount
    const cryptoAmount = parseFloat(amount);
    if (Number.isNaN(cryptoAmount) || cryptoAmount <= 0) {
      console.error(`[IPN:${requestId}] Invalid amount received: ${amount} -> ${cryptoAmount}`);
      throw new Error('Invalid amount');
    }

    // Convert to USD
    let usdAmount;
    try {
      usdAmount = await convertToUSD(cryptoAmount, currency);
      if (Number.isNaN(usdAmount) || usdAmount <= 0) {
        throw new Error(`Invalid conversion result: ${usdAmount}`);
      }
    } catch (conversionError) {
      console.error(`[IPN:${requestId}] Failed to convert amount to USD:`, {
        cryptoAmount,
        currency,
        error: conversionError.message,
      });
      throw new Error('Currency conversion failed');
    }

    const newBalance = currentBalance + usdAmount;


    // Create transaction record
    const transactionData = {
      internal_id: txInternalId,
      created_at: new Date().toISOString(),
      team_internal_id: teamInternalId,
      team_id: teamId,
      amount: usdAmount,
      description: `${cryptoAmount} ${currency}`,
      balance_before: currentBalance,
      balance_after: newBalance,
    };

    // Check if the transaction already exists to avoid duplicate balance updates
    const transactionRepository = getTransactionRepository();
    const existingTxResult = await transactionRepository.existsByInternalId(txInternalId);

    if (existingTxResult) {
      console.warn(
        `[IPN:${requestId}] Duplicate notification received, transaction already exists. Skipping balance update.`
      );
      return { success: true, message: 'Duplicate notification ignored' };
    }

    // Insert new transaction using repository
    const transactionResult = await transactionRepository.createWithValidation(transactionData, true);

    if (!transactionResult.success) {
      console.error(
        `[IPN:${requestId}] Error inserting transaction record:`,
        transactionResult.error
      );
      // Check if this is a duplicate key error (race condition)
      if (transactionResult.error?.message?.includes('duplicate') || 
          transactionResult.error?.code === '23505') {
        console.warn(`[IPN:${requestId}] Duplicate transaction detected during creation (race condition)`);
        return { success: true, message: 'Duplicate notification ignored (race condition)' };
      }
      throw new Error('Failed to record transaction');
    }

    const transaction = transactionResult.data;

    // Attempt to update balance using repository with optimistic concurrency check
    const balanceUpdateResult = await teamRepository.updateBalanceWithConcurrencyCheck(
      teamId,
      newBalance,
      currentBalance
    );

    if (!balanceUpdateResult.success) {
      console.error(
        `[IPN:${requestId}] Failed to update team balance (may be concurrent update):`,
        balanceUpdateResult.error
      );
      // Log this for manual reconciliation but don't fail the entire process
      console.warn(`[IPN:${requestId}] Transaction recorded but balance update failed - manual reconciliation may be needed:`, {
        teamId,
        transactionId: txInternalId,
        expectedBalance: newBalance,
        currentBalance: currentBalance
      });
    }

    // Send notification to team devices with localization
    try {
      // Get device language preference for the team
      const deviceRepository = getDeviceRepository();
      const devicesResult = await deviceRepository.findByTeamId(teamId);
      
      const lang = devicesResult.success && devicesResult.data.length > 0 ? (devicesResult.data[0].lang || 'en') : 'en';
      
      // Generate localized notification using template
      const notification = getPaymentReceivedNotification(teamId, cryptoAmount, currency, usdAmount, newBalance, lang);
      
      // Add additional data fields for backward compatibility
      notification.data = {
        ...notification.data,
        address: address,
        transaction_id: id.toString(),
        blockchain_hash: blockchain_hash || '',
        status: status,
        balance_before: currentBalance.toString(),
      };

      await sendNotification(notification);

    } catch (notificationError) {
      console.error(`[IPN:${requestId}] Failed to send notification:`, notificationError);
    }

    // Return success response
    const response = {
      success: true,
      message: 'Payment notification processed successfully',
      transaction_id: id.toString(),
      crypto_amount: cryptoAmount,
      usd_amount: usdAmount,
      currency: currency,
      balance_before: currentBalance,
      balance_after: newBalance,
    };
    return response;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error(`[IPN:${requestId}] FATAL ERROR after ${processingTime}ms:`, {
        error: error.message,
        stack: error.stack,
        name: error.name,
        timestamp: new Date().toISOString()
      });
      
      // Log the current state for debugging
      console.error(`[IPN:${requestId}] Error context:`, {
        hasRequest: !!request,
        requestMethod: request?.method,
        requestUrl: request?.url,
        userAgent: request?.headers?.get('user-agent'),
        contentType: request?.headers?.get('content-type')
      });
      
      // Re-throw the error to be handled by the calling code
      throw error;
    }
  }

  /**
   * Get IPN endpoint info (for testing)
   * @returns {Object} Endpoint information
   */
  getIpnInfo() {
    return {
      endpoint: 'WestWallet IPN Webhook',
      status: 'active',
      authorized_ip: WESTWALLET_IPN_IP,
    };
  }
}