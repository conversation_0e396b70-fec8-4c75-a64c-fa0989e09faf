// src/routes/api/[...slugs]/lib/notifications/routes.js
import { t } from 'elysia';

/**
 * Configure notifications-related routes
 * @param {import('elysia').Elysia} app - The Elysia app
 * @param {import('./handlers.js').NotificationsHandlers} notificationsHandlers - Notifications handlers
 * @returns {import('elysia').Elysia} The configured app
 */
export function configureNotificationsRoutes(app, notificationsHandlers) {
  return app
    .post(
      '/notifications/token',
      async ({ body, headers, set }) => {
        try {
          const result = await notificationsHandlers.updateFcmToken(body, headers);
          return result;
        } catch (error) {
          const status = error.message === 'Unauthorized' ? 401 : 
                        error.message.includes('Missing') ? 400 : 500;
          set.status = status;
          return {
            error: error instanceof Error ? error.message : 'Unknown error',
          };
        }
      },
      {
        body: t.Object({
          token: t.String(),
          customer_id: t.String(),
          lang: t.Optional(t.String()),
        }),
        detail: {
          summary: 'Update FCM token and language for device',
          tags: ['Notifications'],
        },
      }
    )
    .post(
      '/notifications/ipn',
      async ({ request, set }) => {
        try {
          const result = await notificationsHandlers.handleIpnWebhook(request);
          return result;
        } catch (error) {
          const status = error.message === 'Unauthorized IP address' ? 401 :
                        error.message.includes('Missing') || 
                        error.message.includes('Invalid') ? 400 :
                        error.message.includes('not found') ? 404 : 500;
          set.status = status;
          return {
            error: error instanceof Error ? error.message : 'Unknown error',
            details: error instanceof Error ? error.message : 'Unknown error',
          };
        }
      },
      {
        detail: {
          summary: 'Handle IPN webhook from WestWallet',
          tags: ['Notifications'],
        },
      }
    )
    .get(
      '/notifications/ipn',
      () => {
        return notificationsHandlers.getIpnInfo();
      },
      {
        detail: {
          summary: 'Get IPN endpoint information',
          tags: ['Notifications'],
        },
      }
    );
}