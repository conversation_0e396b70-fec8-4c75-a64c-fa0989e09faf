// src/routes/api/[...slugs]/lib/vpn/routes.js
import { t } from 'elysia';
import { authenticate } from '../helpers.js';

/**
 * Configure VPN-related routes
 * @param {import('elysia').Elysia} app - The Elysia app
 * @param {import('./handlers.js').VpnHandlers} vpnHandlers - VPN handlers
 * @returns {import('elysia').Elysia} The configured app
 */
export function configureVpnRoutes(app, vpnHandlers) {
  return app
    .post(
      '/vpn',
      ({ body, headers, set, request }) => {
        try {
          const result = vpnHandlers.handleVpnUpdate(body, headers, request);
          return result;
        } catch (error) {
          set.status = 401;
          return {
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          };
        }
      },
      {
        body: t.Object({
          clients: t.Array(
            t.Object({
              client_id: t.String(),
              allowed_ips: t.String(),
              enabled: t.<PERSON>(),
            })
          ),
        }),
        detail: {
          summary: 'Update clients from a node',
          tags: ['VPN'],
        },
      }
    )
    .post(
      '/client/:clientId/status',
      ({ params, body, headers, set }) => {
        try {
          return vpnHandlers.setClientStatus(
            params.clientId,
            body.enabled,
            headers
          );
        } catch (error) {
          set.status =
            error instanceof Error && error.message.includes('not found')
              ? 404
              : 401;
          return {
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          };
        }
      },
      {
        params: t.Object({
          clientId: t.String(),
        }),
        body: t.Object({
          enabled: t.Boolean(),
        }),
        detail: {
          summary: 'Set client enabled/disabled status',
          tags: ['VPN'],
        },
      }
    )
    .post(
      '/client/:clientId/acknowledge',
      ({ params, body, headers, set }) => {
        try {
          authenticate({ headers });
          return vpnHandlers.acknowledgeUpdate(
            params.clientId,
            body.updateId,
            body.success
          );
        } catch (error) {
          set.status =
            error instanceof Error && error.message.includes('not found')
              ? 404
              : 401;
          return {
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          };
        }
      },
      {
        params: t.Object({
          clientId: t.String(),
        }),
        body: t.Object({
          updateId: t.String(),
          success: t.Boolean(),
        }),
        detail: {
          summary: 'Acknowledge client status update application',
          tags: ['VPN'],
        },
      }
    )
    .get(
      '/clients',
      ({ headers, set }) => {
        try {
          authenticate({ headers });
          return vpnHandlers.getAllClients();
        } catch (error) {
          set.status = 401;
          return {
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error',
          };
        }
      },
      {
        detail: {
          summary: 'Get all clients',
          tags: ['VPN'],
        },
      }
    )
    .post(
      '/client/:clientId/trust-node',
      ({ params, headers, set }) => {
        try {
          return vpnHandlers.trustNodeState(params.clientId, headers);
        } catch (error) {
          set.status =
            error instanceof Error && error.message.includes('not found')
              ? 404
              : 401;
          return {
            success: false,
            message: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString(),
          };
        }
      },
      {
        params: t.Object({
          clientId: t.String(),
        }),
        detail: {
          summary: "Trust node's state to resolve conflict",
          tags: ['VPN'],
        },
      }
    );
}