// src/routes/api/[...slugs]/lib/vpn/handlers.js
import { getClientIP, authenticate } from '../helpers.js';

/**
 * VPN-related API handlers
 */
export class VpnHandlers {
  /**
   * @param {import('./database.js').ClientDatabase} db - The client database
   */
  constructor(db) {
    this.db = db;
  }

  /**
   * Handle VPN update request with two-way communication
   * @param {Object} body - The request body
   * @param {import('../../lib/helpers').ClientRequest[]} body.clients - List of clients
   * @param {Object} headers - The request headers
   * @param {string} [headers.authorization] - The authorization header
   * @param {Request} request - The HTTP request
   * @returns {Object} The response
   */
  handleVpnUpdate(body, headers, request) {
    authenticate({ headers });
    // Use the node's IP as its ID
    const nodeIp = getClientIP(request);

    // Update database with client data
    this.db.updateFromNode(nodeIp, body.clients);

    // Get any pending updates for clients on this node
    const clientUpdates = this.db
      .getPendingClientUpdates(nodeIp)
      .map((update) => ({
        ...update,
        timestamp: update.timestamp.toISOString(), // Convert Date to string for JSON
      }));

    return {
      success: true,
      message: `Updated ${body.clients.length} clients from node ${nodeIp}`,
      nodeIp,
      timestamp: new Date().toISOString(),
      // Send any pending configuration changes back to the node
      client_updates: clientUpdates,
    };
  }

  /**
   * Set client status (enabled/disabled)
   * @param {string} clientId - The client ID
   * @param {boolean} enabled - Whether the client should be enabled
   * @param {Object} headers - The request headers
   * @param {string} [headers.authorization] - The authorization header
   * @param {string} [userId] - The user making the change
   * @returns {Object} The response
   */
  setClientStatus(clientId, enabled, headers, userId) {
    authenticate({ headers });

    // Update client status in database
    const result = this.db.updateClientStatus(clientId, enabled, userId);

    if (result) {
      return {
        success: true,
        message: `Client ${clientId} ${enabled ? 'enabled' : 'disabled'} successfully`,
        timestamp: new Date().toISOString(),
      };
    } else {
      throw new Error(`Client ${clientId} not found`);
    }
  }

  /**
   * Acknowledge client status update application
   * @param {string} clientId - The client ID
   * @param {string} updateId - The update ID
   * @param {boolean} [success=true] - Whether the update was successful
   * @returns {Object} The response
   */
  acknowledgeUpdate(clientId, updateId, success = true) {
    const client = this.db.getClient(clientId);

    if (!client) {
      throw new Error(`Client ${clientId} not found`);
    }

    const pendingUpdates = this.db.getPendingUpdatesForClient(clientId);
    const matchingUpdate = pendingUpdates.find((u) => u.updateId === updateId);

    if (!matchingUpdate) {
      return {
        success: false,
        message: `No pending update found with ID ${updateId}`,
        timestamp: new Date().toISOString(),
      };
    }

    // Handle the acknowledgment
    if (success) {
      // Mark as applied in history
      for (const entry of client.statusHistory) {
        if (entry.timestamp.getTime() === parseInt(updateId.split('-')[1])) {
          entry.appliedAt = new Date();
          break;
        }
      }

      // Complete the sync if this was a status update
      if (matchingUpdate.type === 'status') {
        client.actualState = client.desiredState;
        client.pendingSync = false;
      }

      // Remove this specific update
      this.db.removePendingUpdate(clientId, updateId);

      return {
        success: true,
        message: `Update ${updateId} for client ${clientId} acknowledged`,
        timestamp: new Date().toISOString(),
      };
    } else {
      // Handle failed update
      return {
        success: false,
        message: `Node reported failure applying update`,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Get all clients
   * @returns {Object} The response with all clients
   */
  getAllClients() {
    const clients = this.db.getAllClients().map((client) => ({
      id: client.id,
      ips: client.ips,
      nodeId: client.nodeId,
      lastUpdate: client.lastUpdate,
      status: {
        desired: client.desiredState ? 'enabled' : 'disabled',
        actual: client.actualState ? 'enabled' : 'disabled',
        pending: client.pendingSync,
        lastChanged: client.lastStateChange,
        syncStatus: client.pendingSync
          ? 'pending'
          : client.desiredState === client.actualState
            ? 'synced'
            : 'conflict',
      },
    }));

    return {
      clients,
      count: clients.length,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Trust node's state (resolve conflict by making node state authoritative)
   * @param {string} clientId - The client ID
   * @param {Object} headers - The request headers
   * @param {string} [headers.authorization] - The authorization header
   * @param {string} [userId] - The user making the change
   * @returns {Object} The response
   */
  trustNodeState(clientId, headers, userId) {
    authenticate({ headers });

    // Update client to trust node's state
    const result = this.db.trustNodeState(clientId, userId);

    if (result) {
      return {
        success: true,
        message: `Client ${clientId} updated to trust node's state`,
        timestamp: new Date().toISOString(),
      };
    } else {
      // If no update was made, it could be because there's no conflict
      const client = this.db.getClient(clientId);
      if (!client) {
        throw new Error(`Client ${clientId} not found`);
      }

      return {
        success: false,
        message: `No conflict to resolve for client ${clientId}`,
        timestamp: new Date().toISOString(),
      };
    }
  }
}