// src/routes/api/[...slugs]/lib/database.js
import { env } from '$env/dynamic/private';

// Avoid setting NODE_TIMEOUT_SECONDS equal to or less than the report interval
// — this leads to false positives in cleanup.
// Always keep it at least 2–3× higher.
const NODE_TIMEOUT_SECONDS = parseInt(env.NODE_TIMEOUT_SECONDS || '30');
const CLEANUP_INTERVAL_SECONDS = parseInt(env.CLEANUP_INTERVAL_SECONDS || '15');

// Singleton instance
let instance = null;

export class ClientDatabase {
  constructor() {
    // Return existing instance if it exists (singleton pattern)
    if (instance) {
      return instance;
    }

    // Initialize maps
    this.clients = new Map(); // Key is client_id
    this.ipToClientMap = new Map(); // IP to client_id mapping
    this.nodes = new Map(); // Node tracking (key is node IP)
    this.pendingUpdates = new Map(); // Pending client updates (key is client_id)
    this.cleanupInterval = null;

    // Start the cleanup timer
    this.startCleanupTimer();

    // Start stalled updates check timer
    setInterval(() => {
      this.checkStalledUpdates();
    }, 60 * 1000); // Check every minute

    // Store as singleton instance
    instance = this;
  }

  // Start cleanup timer
  startCleanupTimer() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.cleanupInterval = setInterval(() => {
      this.cleanupStaleData();
    }, CLEANUP_INTERVAL_SECONDS * 1000);
  }

  // Cleanup stale nodes and their clients
  cleanupStaleData() {
    const now = new Date();
    const staleThreshold = new Date(
      now.getTime() - NODE_TIMEOUT_SECONDS * 1000
    );
    let removedNodes = 0;
    let removedClients = 0;

    // Find stale nodes
    for (const [nodeId, node] of this.nodes.entries()) {
      if (node.lastSeen < staleThreshold) {
        // Remove all clients that came from this node
        for (const clientId of node.clientIds) {
          const client = this.clients.get(clientId);

          if (client) {
            // If this client only exists on this node, remove it completely
            // Remove IP to client mappings first
            client.ips.forEach((ip) => {
              this.ipToClientMap.delete(ip.ip);
            });

            // Then remove the client
            this.clients.delete(clientId);

            // Remove any pending updates for this client
            this.pendingUpdates.delete(clientId);

            removedClients++;
          }
        }

        // Remove the node
        this.nodes.delete(nodeId);
        removedNodes++;
      }
    }

    // Cleanup complete
  }

  // Parse IPs from string format to structured format
  parseIPs(ipsString) {
    return ipsString.split(',').map((ip) => {
      const trimmedIP = ip.trim();
      return {
        ip: trimmedIP,
        type: trimmedIP.includes(':') ? 'ipv6' : 'ipv4',
      };
    });
  }

  // Register or update a node - now using nodeIp as the ID
  registerNode(nodeIp) {
    const now = new Date();
    let node = this.nodes.get(nodeIp);

    if (node) {
      // Update existing node
      node.lastSeen = now;
    } else {
      // Create new node
      node = {
        id: nodeIp,
        lastSeen: now,
        clientIds: new Set(),
      };
      this.nodes.set(nodeIp, node);
    }

    return node;
  }

  /**
   * Update or add clients from a node update
   * @param {string} nodeIp - The IP of the node
   * @param {import('./helpers').ClientRequest[]} clientsData - Client data from the node
   */
  updateFromNode(nodeIp, clientsData) {
    const now = new Date();

    // Register/update the node first
    const node = this.registerNode(nodeIp);

    // Track which clients we're going to update
    const updatedClientIds = new Set();

    // Process all clients from this update
    for (const clientData of clientsData) {
      const clientId = clientData.client_id;
      const parsedIPs = this.parseIPs(clientData.allowed_ips);
      const enabled =
        clientData.enabled !== undefined ? clientData.enabled : true;

      // Add to the updated clients set
      updatedClientIds.add(clientId);

      // Create or update client entry
      const existingClient = this.clients.get(clientId);

      if (existingClient) {
        // Update existing client info
        existingClient.ips = parsedIPs;
        existingClient.nodeId = nodeIp;
        existingClient.lastUpdate = now;

        // Update actual state but NEVER override the desired state
        existingClient.actualState = enabled;

        // If no pending sync and states match, ensure pendingSync is false
        if (existingClient.desiredState === enabled) {
          existingClient.pendingSync = false;
        }
      } else {
        // Create new client - initially both states are the same
        const newClient = {
          id: clientId,
          ips: parsedIPs,
          nodeId: nodeIp,
          lastUpdate: now,
          desiredState: enabled,
          actualState: enabled,
          pendingSync: false,
          lastStateChange: now,
          statusHistory: [],
        };
        this.clients.set(clientId, newClient);
      }

      // Update IP to client mappings
      parsedIPs.forEach((ip) => {
        this.ipToClientMap.set(ip.ip, clientId);
      });

      // Associate this client with the node
      node.clientIds.add(clientId);
    }

    // Clean up any clients that were previously reported by this node but aren't in this update
    // This handles clients that have been removed from the node's config
    const clientsToRemove = new Set();

    for (const existingClientId of node.clientIds) {
      if (!updatedClientIds.has(existingClientId)) {
        clientsToRemove.add(existingClientId);
      }
    }

    // Remove the clients that are no longer reported by this node
    for (const clientIdToRemove of clientsToRemove) {
      const client = this.clients.get(clientIdToRemove);

      if (client) {
        // Delete IP mappings first
        client.ips.forEach((ip) => {
          this.ipToClientMap.delete(ip.ip);
        });

        // Then delete the client
        this.clients.delete(clientIdToRemove);
        node.clientIds.delete(clientIdToRemove);

        // Remove any pending updates
        this.pendingUpdates.delete(clientIdToRemove);
      }
    }
  }

  updateClientStatus(clientId, enabled, userId) {
    const client = this.clients.get(clientId);

    if (!client) {
      return false;
    }

    // Only create an update if the desired state changes
    if (client.desiredState !== enabled) {
      const now = new Date();

      // Record the change in history
      client.statusHistory.push({
        timestamp: now,
        from: client.desiredState,
        to: enabled,
        userId,
      });

      // Update the client record
      client.desiredState = enabled;
      client.pendingSync = true;
      client.lastStateChange = now;

      // Create a unique update ID
      const updateId = `${clientId}-${now.getTime()}`;

      // Add to pending updates for the node
      let updates = this.pendingUpdates.get(clientId) || [];
      updates = updates.filter((u) => u.type !== 'status'); // Remove any existing status updates

      // Add the new update
      updates.push({
        type: 'status',
        client_id: clientId,
        enabled: enabled,
        updateId,
        timestamp: now,
      });

      this.pendingUpdates.set(clientId, updates);
    }

    return true;
  }

  trustNodeState(clientId, userId) {
    const client = this.clients.get(clientId);

    if (!client) {
      return false;
    }

    // Only proceed if there's a conflict (desired state != actual state)
    if (client.desiredState !== client.actualState) {
      const now = new Date();

      // Record the change in history with a special note that this was a "trust node" action
      client.statusHistory.push({
        timestamp: now,
        from: client.desiredState,
        to: client.actualState,
        userId,
        note: 'Trusted node state to resolve conflict',
      });

      // Update the desired state to match the actual state
      client.desiredState = client.actualState;
      client.pendingSync = false; // No need to sync since we're adopting the node's state
      client.lastStateChange = now;

      // Remove any pending updates for this client since we're trusting the node
      this.pendingUpdates.delete(clientId);
      return true;
    }

    // No conflict to resolve
    return false;
  }

  /**
   * Get pending updates for clients on a specific node
   * @param {string} nodeIp - The IP of the node
   * @returns {import('./helpers').ClientUpdate[]} List of pending updates
   */
  getPendingClientUpdates(nodeIp) {
    const node = this.nodes.get(nodeIp);

    if (!node) {
      return [];
    }

    const updates = [];
    const now = new Date();

    // Collect all pending updates for clients on this node
    for (const clientId of node.clientIds) {
      const client = this.clients.get(clientId);
      const clientUpdates = this.pendingUpdates.get(clientId);

      if (client && clientUpdates && clientUpdates.length > 0) {
        client.lastSyncAttempt = now;
        updates.push(...clientUpdates);
      }
    }

    // Return the updates

    return updates;
  }

  /**
   * Get all clients
   * @returns {import('./helpers').Client[]} List of all clients
   */
  getAllClients() {
    return Array.from(this.clients.values());
  }

  /**
   * Get client by ID
   * @param {string} clientId - The client ID
   * @returns {import('./helpers').Client|undefined} The client or undefined
   */
  getClient(clientId) {
    return this.clients.get(clientId);
  }

  /**
   * Get pending updates for a specific client
   * @param {string} clientId - The client ID
   * @returns {import('./helpers').ClientUpdate[]} List of pending updates
   */
  getPendingUpdatesForClient(clientId) {
    return this.pendingUpdates.get(clientId) || [];
  }

  /**
   * Remove a specific pending update
   * @param {string} clientId - The client ID
   * @param {string} updateId - The update ID
   * @returns {boolean} Whether the update was removed
   */
  removePendingUpdate(clientId, updateId) {
    const updates = this.pendingUpdates.get(clientId);

    if (!updates) {
      return false;
    }

    const initialLength = updates.length;
    const filteredUpdates = updates.filter(
      (update) => update.updateId !== updateId
    );

    if (filteredUpdates.length < initialLength) {
      this.pendingUpdates.set(clientId, filteredUpdates);
      return true;
    }

    return false;
  }

  // Check for stalled updates
  checkStalledUpdates() {
    const now = new Date();
    const timeoutThreshold = 5 * 60 * 1000; // 5 minutes

    for (const client of this.clients.values()) {
      if (
        client.pendingSync &&
        client.lastSyncAttempt &&
        now.getTime() - client.lastSyncAttempt.getTime() > timeoutThreshold
      ) {
        // Client sync has been pending for over 5 minutes
        // Here you could trigger notifications, emails, etc.
      }
    }
  }

  /**
   * Get all nodes with status
   
   */
  getAllNodes() {
    return Array.from(this.nodes.values()).map((node) => ({
      ...node,
      clientCount: node.clientIds.size,
    }));
  }

  /**
   * Get client by IP
   * @param {string} ip - The IP address
   * @returns {import('./helpers').Client|undefined} The client or undefined
   */
  getClientByIP(ip) {
    const clientId = this.ipToClientMap.get(ip);
    if (clientId) {
      return this.clients.get(clientId);
    }
    return undefined;
  }

  /**
   * Get the singleton instance of ClientDatabase
   * @returns {ClientDatabase} The singleton instance
   */
  static getInstance() {
    if (!instance) {
      instance = new ClientDatabase();
    }
    return instance;
  }

  /**
   * Reset the singleton instance (mainly for testing)
   */
  static resetInstance() {
    if (instance && instance.cleanupInterval) {
      clearInterval(instance.cleanupInterval);
    }
    instance = null;
  }
}
