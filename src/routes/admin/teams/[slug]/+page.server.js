import { error, redirect } from '@sveltejs/kit';
import { getTeamRepository, getDeviceRepository } from '$lib/server/database/DatabaseFactory.js';
import { QueryOptions } from '$lib/server/database/interfaces/IDatabase.js';
import { getTeamWallets } from '$lib/server/walletService.js';
import {
  deleteTeamWithAssociatedData,
  getTeamStatistics,
} from '$lib/server/teamService.js';

// SvelteKit page server for /team/new (no server data needed)
export const load = async ({ params }) => {
  try {
    const id = params.slug;
    const teamRepository = getTeamRepository();
    const deviceRepository = getDeviceRepository();

    // Fetch team data
    const teamResult = await teamRepository.findByTeamId(id);

    if (!teamResult.success || !teamResult.data) {
      console.error('Error fetching team:', teamResult.error);
      throw error(404, 'Team not found');
    }

    // Fetch devices for this team
    const devicesResult = await deviceRepository.findByTeamId(id, new QueryOptions({
      orderBy: { created_at: 'desc' }
    }));

    if (!devicesResult.success) {
      console.error('Error fetching devices:', devicesResult.error);
      // Don't throw error, just return empty devices array
    }

    // Fetch wallets for this team
    let wallets = [];
    try {
      wallets = await getTeamWallets(id);
    } catch (walletError) {
      console.error('Error fetching wallets:', walletError);
      // Don't throw error, just return empty wallets array
    }

    // Get team statistics for deletion confirmation
    const statistics = await getTeamStatistics(id);

    // Helper function to safely convert dates to ISO strings
    const safeToISOString = (date) => {
      if (!date) return null;
      if (typeof date === 'string') return date;
      if (date instanceof Date) return date.toISOString();
      return new Date(date).toISOString();
    };

    // Ensure all data is properly serializable
    const safeTeam = {
      id: teamResult.data.id || '',
      internal_id: teamResult.data.internal_id || '',
      charge_amount: teamResult.data.charge_amount || 0,
      balance: teamResult.data.balance || 0,
      created_at: safeToISOString(teamResult.data.created_at) || new Date().toISOString(),
      next_charge_at: safeToISOString(teamResult.data.next_charge_at),
      owner_id: teamResult.data.owner_id || null,
      owner_internal_id: teamResult.data.owner_internal_id || null,
    };

    // Convert devices to plain objects
    const safeDevices = (devicesResult.data || []).map(device => ({
      internal_id: device.internal_id || '',
      team_id: device.team_id || '',
      team_internal_id: device.team_internal_id || '',
      ip: device.ip || '',
      nickname: device.nickname || '',
      role: device.role || '',
      created_at: safeToISOString(device.created_at) || new Date().toISOString(),
      last_auth_at: safeToISOString(device.last_auth_at),
      vpn_conf: device.vpn_conf || '',
      msg_conf: device.msg_conf || '',
      phone_conf: device.phone_conf || '',
      fcm_token: device.fcm_token || null,
      sip_id: device.sip_id || null,
    }));

    // Convert wallets to plain objects
    const safeWallets = (wallets || []).map(wallet => ({
      internal_id: wallet.internal_id || '',
      team_id: wallet.team_id || '',
      team_internal_id: wallet.team_internal_id || '',
      currency: wallet.currency || '',
      address: wallet.address || '',
      created_at: safeToISOString(wallet.created_at) || new Date().toISOString(),
    }));

    return {
      team: safeTeam,
      devices: safeDevices,
      wallets: safeWallets,
      statistics,
    };
  } catch (err) {
    console.error('Error in team detail load function:', err);
    throw err;
  }
};

// Add actions for PUT and DELETE
export const actions = {
  delete: async ({ params }) => {
    const id = params.slug;

    const result = await deleteTeamWithAssociatedData(id);

    if (!result.success) {
      throw error(500, result.error || 'Failed to delete team');
    }

    throw redirect(303, '/admin/teams');
  },
  update: async ({ request, params }) => {
    const currentId = params.slug;
    const formData = await request.formData();
    const teamRepository = getTeamRepository();
    /** @type {Record<string, any>} */
    const updateFields = {};

    // Extract fields from form data
    const newId = formData.get('id');
    const charge_amount = formData.get('charge_amount');
    const balance = formData.get('balance');
    const role = formData.get('role');
    const magnus_key = formData.get('magnus_key');
    const magnus_secret = formData.get('magnus_secret');
    // Add other fields if needed, ensure they are allowed update fields in your schema
    // ['created_at', 'next_charge_at', 'owner_internal_id', 'owner_id'] are usually not editable via this form

    if (newId !== null && newId !== currentId) {
      // If ID is being changed, add it to updateFields
      updateFields.id = newId;
    }

    if (charge_amount !== null) {
      // Convert charge_amount to number if it exists
      updateFields.charge_amount = parseFloat(charge_amount.toString());
      if (isNaN(updateFields.charge_amount)) {
        return { success: false, error: 'Invalid charge amount value' };
      }
    }

    if (balance !== null) {
      // Convert balance to number if it exists
      updateFields.balance = parseFloat(balance.toString());
      if (isNaN(updateFields.balance)) {
        return { success: false, error: 'Invalid balance value' };
      }
    }

    if (role !== null) {
      // Only update role if a value is provided (handle the 'Select Role' option if it sends null/empty string)
      // Assuming 'Select Role' option sends an empty string or null, adjust if needed
      updateFields.role = role === '' ? null : role;
    }

    if (magnus_key !== null) {
      // Handle magnus_key - allow empty string to clear the field
      updateFields.magnus_key = magnus_key.toString().trim() === '' ? null : magnus_key.toString();
    }

    if (magnus_secret !== null) {
      // Handle magnus_secret - allow empty string to clear the field
      updateFields.magnus_secret = magnus_secret.toString().trim() === '' ? null : magnus_secret.toString();
    }

    if (Object.keys(updateFields).length === 0) {
      return { success: false, error: 'No valid fields to update' };
    }

    // Use repository to update the team
    const updateResult = await teamRepository.updateById(currentId, updateFields);

    if (!updateResult.success) {
      console.error('Team update error:', updateResult.error);
      // Check for specific errors like unique constraint violation for ID
      if (updateResult.error?.code === '23505') {
        // PostgreSQL unique violation error code
        return {
          success: false,
          error: `Customer ID "${newId}" already exists.`,
        };
      }
      throw error(500, updateResult.error?.message || 'Failed to update team');
    }

    throw redirect(303, `/admin/teams`);
  },
};
