<script>
    import { goto } from "$app/navigation";
    import { timeAgo } from '$lib/utils/dateUtils';

    export let data;

    let team = data.team ? data.team : null;
    let transactions = data.transactions || [];

    function formatDate(dateString) {
        if (!dateString) return "-";
        try {
            const date = new Date(dateString);
            return date.toLocaleString(undefined, {
                year: "numeric",
                month: "numeric",
                day: "numeric",
                hour: "2-digit",
                minute: "2-digit",
            });
        } catch {
            return "Invalid date";
        }
    }

    function formatAmount(amount) {
        if (!amount || amount === 0) return "0.00";
        return parseFloat(amount).toFixed(2);
    }

    function getAmountClass(amount) {
        if (!amount || amount === 0) return '';
        return amount > 0 ? 'amount-positive' : 'amount-negative';
    }
</script>

<svelte:head>
  <title>Phantom | Team Transactions - {team?.id || 'Unknown'}</title>
</svelte:head>

<div class="admin-container">
    {#if team}
        <div class="admin-card">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <h1 class="admin-title">Transactions for Team: {team.id}</h1>
                <button class="button button-secondary" on:click={() => goto(`/admin/teams/${team.id}`)}>
                    ← Back to Team
                </button>
            </div>

            <hr style="margin-top: 0.5rem; border-color: #333;" />

            <!-- Team Summary -->
            <div class="team-summary" style="margin-bottom: 2rem; padding: 1rem; background: #1a1a1a; border-radius: 8px;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div>
                        <strong>Team ID:</strong> {team.id}
                    </div>
                    <div>
                        <strong>Balance:</strong> ${team.balance.toFixed(2)}
                    </div>
                    <div>
                        <strong>Total Transactions:</strong> {transactions.length}
                    </div>
                </div>
            </div>

            <!-- Transactions Section -->
            <div class="section">
                <h2>Transaction History ({transactions.length})</h2>
                {#if transactions.length > 0}
                    <div style="overflow-x: auto;">
                        <table class="client-table">
                            <thead>
                                <tr>
                                    <th style="padding: 8px 4px;">Date</th>
                                    <th style="padding: 8px 4px;">Amount ($)</th>
                                    <th style="padding: 8px 4px;">Description</th>
                                    <th style="padding: 8px 4px;">Balance Before</th>
                                    <th style="padding: 8px 4px;">Balance After</th>
                                </tr>
                            </thead>
                            <tbody>
                                {#each transactions as transaction}
                                    <tr>
                                        <td style="padding: 8px 4px; font-size: 0.9em;">
                                            {formatDate(transaction.created_at)}
                                        </td>
                                        <td style="padding: 8px 4px; font-family: monospace; font-weight: bold;" class={getAmountClass(transaction.amount)}>
                                            {formatAmount(transaction.amount)}
                                        </td>
                                        <td style="padding: 8px 4px;">
                                            {transaction.description || '-'}
                                        </td>
                                        <td style="padding: 8px 4px; font-family: monospace;">
                                            ${formatAmount(transaction.balance_before)}
                                        </td>
                                        <td style="padding: 8px 4px; font-family: monospace;">
                                            ${formatAmount(transaction.balance_after)}
                                        </td>
                                    </tr>
                                {/each}
                            </tbody>
                        </table>
                    </div>
                {:else}
                    <div class="empty-state">
                        <p>No transactions found for this team.</p>
                        <p style="font-size: 0.9em; margin-top: 0.5rem; color: #a0aec0;">
                            Transactions will appear here when payments are made to the team's wallets.
                        </p>
                    </div>
                {/if}
            </div>
        </div>
    {:else}
        <p class="empty-state">
            Loading team data or team not found...
        </p>
    {/if}
</div>

<style>
    .amount-positive {
        color: #22c55e;
    }

    .amount-negative {
        color: #ef4444;
    }

    .text-muted {
        color: #a0aec0;
    }

    .team-summary {
        border: 1px solid #333;
    }
</style>