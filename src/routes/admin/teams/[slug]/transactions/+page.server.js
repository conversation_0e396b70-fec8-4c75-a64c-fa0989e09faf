import { error } from '@sveltejs/kit';
import { getTeamRepository, getTransactionRepository } from '$lib/server/database/DatabaseFactory.js';
import { QueryOptions } from '$lib/server/database/interfaces/IDatabase.js';

export const load = async ({ params }) => {
  try {
    const teamId = params.slug;
    const teamRepository = getTeamRepository();
    const transactionRepository = getTransactionRepository();

    // Fetch team data to verify it exists
    const teamResult = await teamRepository.findByTeamId(teamId);

    if (!teamResult.success || !teamResult.data) {
      console.error('Error fetching team:', teamResult.error);
      throw error(404, 'Team not found');
    }

    // Fetch transactions for this team
    const transactionsResult = await transactionRepository.findByTeamId(teamId, new QueryOptions({
      orderBy: { created_at: 'desc' }
    }));

    if (!transactionsResult.success) {
      console.error('Error fetching transactions:', transactionsResult.error);
      // Don't throw error, just return empty transactions array
    }

    // Helper function to safely convert dates to ISO strings
    const safeToISOString = (date) => {
      if (!date) return null;
      if (typeof date === 'string') return date;
      if (date instanceof Date) return date.toISOString();
      return new Date(date).toISOString();
    };

    // Ensure team data is properly serializable
    const safeTeam = {
      id: teamResult.data.id || '',
      internal_id: teamResult.data.internal_id || '',
      charge_amount: teamResult.data.charge_amount || 0,
      balance: teamResult.data.balance || 0,
      created_at: safeToISOString(teamResult.data.created_at) || new Date().toISOString(),
      next_charge_at: safeToISOString(teamResult.data.next_charge_at),
      owner_id: teamResult.data.owner_id || null,
      owner_internal_id: teamResult.data.owner_internal_id || null,
    };

    // Convert transactions to plain objects
    const safeTransactions = (transactionsResult.data || []).map(transaction => ({
      internal_id: transaction.internal_id || '',
      team_id: transaction.team_id || '',
      team_internal_id: transaction.team_internal_id || '',
      amount: transaction.amount || 0,
      description: transaction.description || '',
      balance_before: transaction.balance_before || 0,
      balance_after: transaction.balance_after || 0,
      created_at: safeToISOString(transaction.created_at) || new Date().toISOString(),
    }));

    return {
      team: safeTeam,
      transactions: safeTransactions,
    };
  } catch (err) {
    console.error('Error in team transactions load function:', err);
    throw err;
  }
};