import {
  getTeamRepository,
  getDeviceRepository,
  getWalletRepository,
  getTransactionRepository
} from '$lib/server/database/DatabaseFactory.js';
import { QueryOptions } from '$lib/server/database/interfaces/IDatabase.js';

/** @type {import('./$types').PageServerLoad} */
export async function load({ parent }) {
  // Get parent data which includes authentication status
  await parent(); // This ensures layout authentication check runs first

  try {
    // Calculate date ranges for analytics
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
    const last7Days = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const last30Days = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    // Get repository instances
    const teamRepository = getTeamRepository();
    const deviceRepository = getDeviceRepository();
    const walletRepository = getWalletRepository();
    const transactionRepository = getTransactionRepository();

    // Fetch comprehensive overview statistics using repositories
    const [
      teamStatsResult,
      deviceStatsResult,
      walletStatsResult,
      transactionStatsResult,
      recentTeamsResult,
      lastMonthTeamsResult,
      activeDevicesResult,
      topTeamsResult,
      recentTransactionsResult,
      last7DaysTransactionsResult
    ] = await Promise.all([
      // Get comprehensive statistics from repositories
      teamRepository.getStatistics({
        since: last30Days,
        lowBalanceThreshold: 10,
        topTeamsLimit: 5
      }),
      deviceRepository.getStatistics({
        since: last7Days,
        activeHoursThreshold: 168 // 7 days
      }),
      walletRepository.getStatistics({
        since: last30Days
      }),
      transactionRepository.getStatistics({
        since: last30Days,
        until: now
      }),

      // Growth metrics
      teamRepository.findRecentTeams(startOfMonth),
      teamRepository.findRecentTeams(startOfLastMonth, new QueryOptions({
        where: { created_at: { lt: startOfMonth.toISOString() } }
      })),

      // Device analytics
      deviceRepository.findRecentlyActive(last7Days),

      // Top performers
      teamRepository.findTopTeamsByBalance(5),

      // Recent transactions
      transactionRepository.findRecent(last30Days, new QueryOptions({ limit: 100 })),
      transactionRepository.findRecent(last7Days, new QueryOptions({ limit: 100 }))
    ]);

    // Fetch new analytics for charge_amount and lang
    const [chargeAmountStatsResult, deviceLangDistributionResult] = await Promise.all([
      teamRepository.getChargeAmountStatistics(5),
      deviceRepository.getLanguageDistribution()
    ]);

    // Extract data from repository results
    const teamStats = teamStatsResult.success ? teamStatsResult.data : null;
    const deviceStats = deviceStatsResult.success ? deviceStatsResult.data : null;
    const walletStats = walletStatsResult.success ? walletStatsResult.data : null;
    const transactionStats = transactionStatsResult.success ? transactionStatsResult.data : null;

    // Calculate comprehensive business metrics
    const totalTeams = teamStats?.totalTeams || 0;
    const totalDevices = deviceStats?.totalDevices || 0;
    const totalWallets = walletStats?.totalWallets || 0;
    const totalTransactions = transactionStats?.totalTransactions || 0;
    const newTeamsThisMonth = recentTeamsResult.success ? recentTeamsResult.count : 0;
    const newTeamsLastMonth = lastMonthTeamsResult.success ? lastMonthTeamsResult.count : 0;
    const activeDevices = deviceStats?.activeDevices || 0;
    const teamsWithBalance = teamStats?.teamsWithBalance || 0;
    const lowBalanceTeams = teamStats?.lowBalanceTeams || 0;
    const newDevicesThisWeek = deviceStats?.recentDevices || 0;

    // Financial calculations
    const totalBalance = teamStats?.totalBalance || 0;
    const totalRevenue = transactionStats?.totalVolume || 0;

    // Calculate monthly and weekly revenue from transaction data
    const monthlyRevenue = recentTransactionsResult.success ?
      recentTransactionsResult.data?.reduce((sum, tx) => {
        const amount = parseFloat(tx.amount) || 0;
        return amount > 0 ? sum + amount : sum;
      }, 0) || 0 : 0;

    const weeklyRevenue = last7DaysTransactionsResult.success ?
      last7DaysTransactionsResult.data?.reduce((sum, tx) => {
        const amount = parseFloat(tx.amount) || 0;
        return amount > 0 ? sum + amount : sum;
      }, 0) || 0 : 0;

    // Business intelligence metrics
    const avgBalancePerTeam = totalTeams > 0 ? totalBalance / totalTeams : 0;
    const avgDevicesPerTeam = totalTeams > 0 ? totalDevices / totalTeams : 0;
    const avgRevenuePerTeam = totalTeams > 0 ? totalRevenue / totalTeams : 0;
    const teamGrowthRate = newTeamsLastMonth > 0 ? ((newTeamsThisMonth - newTeamsLastMonth) / newTeamsLastMonth) * 100 : 0;
    const activeTeamRate = totalTeams > 0 ? (teamsWithBalance / totalTeams) * 100 : 0;
    const deviceUtilizationRate = totalDevices > 0 ? (activeDevices / totalDevices) * 100 : 0;

    // Top teams by balance
    const topTeams = topTeamsResult.success ?
      topTeamsResult.data?.map(team => ({
        id: team.id,
        balance: parseFloat(team.balance) || 0
      })) || [] : [];

    // Calculate daily revenue trend for last 7 days
    const dailyRevenueTrend = [];
    if (last7DaysTransactionsResult.success && last7DaysTransactionsResult.data) {
      for (let i = 6; i >= 0; i--) {
        const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
        const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);

        const dayRevenue = last7DaysTransactionsResult.data.reduce((sum, tx) => {
          const txDate = new Date(tx.created_at);
          const amount = parseFloat(tx.amount) || 0;
          if (txDate >= dayStart && txDate < dayEnd && amount > 0) {
            return sum + amount;
          }
          return sum;
        }, 0);

        dailyRevenueTrend.push({
          date: dayStart.toISOString().split('T')[0],
          revenue: dayRevenue
        });
      }
    }

    return {
      stats: {
        // Core metrics
        totalTeams,
        totalDevices,
        totalWallets,
        totalTransactions,

        // Growth metrics
        newTeamsThisMonth,
        newTeamsLastMonth,
        teamGrowthRate,
        newDevicesThisWeek,

        // Financial metrics
        totalBalance,
        totalRevenue,
        monthlyRevenue,
        weeklyRevenue,
        avgRevenuePerTeam,

        // Operational metrics
        activeDevices,
        deviceUtilizationRate,
        avgDevicesPerTeam,
        avgBalancePerTeam,

        // Health metrics
        teamsWithBalance,
        lowBalanceTeams,
        activeTeamRate,

        // Analytics
        topTeams,
        dailyRevenueTrend,
        // Add new analytics
        chargeAmountStats: chargeAmountStatsResult.success ? chargeAmountStatsResult.data : null,
        deviceLangDistribution: deviceLangDistributionResult.success ? deviceLangDistributionResult.data : null,
      },
      error: null
    };
  } catch (error) {
    console.error('Error fetching overview data:', error);

    // Return comprehensive fallback data in case of error
    return {
      stats: {
        // Core metrics
        totalTeams: 0,
        totalDevices: 0,
        totalWallets: 0,
        totalTransactions: 0,

        // Growth metrics
        newTeamsThisMonth: 0,
        newTeamsLastMonth: 0,
        teamGrowthRate: 0,
        newDevicesThisWeek: 0,

        // Financial metrics
        totalBalance: 0,
        totalRevenue: 0,
        monthlyRevenue: 0,
        weeklyRevenue: 0,
        avgRevenuePerTeam: 0,

        // Operational metrics
        activeDevices: 0,
        deviceUtilizationRate: 0,
        avgDevicesPerTeam: 0,
        avgBalancePerTeam: 0,

        // Health metrics
        teamsWithBalance: 0,
        lowBalanceTeams: 0,
        activeTeamRate: 0,

        // Analytics
        topTeams: [],
        dailyRevenueTrend: [],
        chargeAmountStats: null,
        deviceLangDistribution: null,
      },
      error: 'Failed to load overview data'
    };
  }
}
