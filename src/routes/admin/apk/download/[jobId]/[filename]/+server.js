import { error } from '@sveltejs/kit';
import fs from 'fs';
import path from 'path';
import { getSession, verifySession } from '$lib/server/session';
import { env } from '$env/dynamic/private';
import { ApkRepackerService } from '$lib/server/ApkRepackerService';

/**
 * Handle GET requests to download repacked APK files
 * @param {Object} params - The request parameters
 * @param {Object} params.params - URL parameters
 * @param {string} params.params.jobId - The job ID
 * @param {string} params.params.filename - The filename to download
 * @param {Object} params.cookies - Request cookies
 * @returns {Promise<Response>} The HTTP response with the file
 */
export async function GET({ params, cookies }) {
  const { jobId, filename } = params;

  // Verify the session (only authenticated users can download)
  const signedSession = cookies.get('admin_session');
  const sessionId = verifySession(signedSession);
  const session = sessionId ? getSession(sessionId) : null;

  // If not authenticated, return error
  if (env.NODE_ENV !== 'development' && !session) {
    throw error(401, 'Authentication required');
  }

  // First, try to find the directory using the jobId as is
  let directoryId = jobId;
  let jobDir = path.join(ApkRepackerService.TMP_DIR, directoryId);

  // If the directory doesn't exist and jobId is numeric, try to find a directory that starts with this jobId
  if (!fs.existsSync(jobDir) && /^\d+$/.test(jobId)) {
    try {
      // List all directories in the TMP_DIR
      const dirs = fs.readdirSync(ApkRepackerService.TMP_DIR);

      // Find directories that start with the jobId
      const matchingDirs = dirs.filter((dir) => dir.startsWith(`${jobId}-`));

      if (matchingDirs.length > 0) {
        // Use the first matching directory
        directoryId = matchingDirs[0];
        jobDir = path.join(ApkRepackerService.TMP_DIR, directoryId);
      }
    } catch (err) {
      console.error(`Error searching for matching directories:`, err);
    }
  }

  // If the directory doesn't exist and jobId contains a dash, try to extract the timestamp
  if (!fs.existsSync(jobDir) && jobId.includes('-')) {
    // Try to extract the timestamp part (should be at the beginning)
    const timestampMatch = jobId.match(/^(\d+)/);
    if (timestampMatch) {
      const timestamp = timestampMatch[1];

      // Try to find a directory that starts with this timestamp
      try {
        // List all directories in the TMP_DIR
        const dirs = fs.readdirSync(ApkRepackerService.TMP_DIR);

        // Find directories that start with the timestamp
        const matchingDirs = dirs.filter((dir) =>
          dir.startsWith(`${timestamp}`)
        );

        if (matchingDirs.length > 0) {
          // Use the first matching directory
          directoryId = matchingDirs[0];
          jobDir = path.join(ApkRepackerService.TMP_DIR, directoryId);
        } else {
          console.warn(
            `No matching directories found for timestamp: ${timestamp}`
          );
        }
      } catch (err) {
        console.error(`Error searching for matching directories:`, err);
      }
    } else {
      console.error(`Invalid job ID format: ${jobId}`);
    }
  }

  // If we still can't find the directory, try one more approach - check if the jobId is part of a directory name
  if (!fs.existsSync(jobDir)) {
    try {
      // List all directories in the TMP_DIR
      const dirs = fs.readdirSync(ApkRepackerService.TMP_DIR);

      // Find directories that contain the jobId
      const matchingDirs = dirs.filter((dir) => dir.includes(jobId));

      if (matchingDirs.length > 0) {
        // Use the first matching directory
        directoryId = matchingDirs[0];
        jobDir = path.join(ApkRepackerService.TMP_DIR, directoryId);
      }
    } catch (err) {
      console.error(`Error searching for matching directories:`, err);
    }
  }

  // Validate filename (prevent directory traversal)
  if (filename.includes('/') || filename.includes('\\')) {
    throw error(400, 'Invalid filename');
  }

  // Construct the file path using the directory ID
  const filePath = path.join(ApkRepackerService.TMP_DIR, directoryId, filename);

  // Log the download attempt for debugging

  // Check if the file exists
  if (!fs.existsSync(filePath)) {
    console.error(`File not found: ${filePath}`);

    // Try to list the contents of the job directory to help diagnose issues
    try {
      const jobDir = path.join(ApkRepackerService.TMP_DIR, directoryId);
      if (fs.existsSync(jobDir)) {
        const files = fs.readdirSync(jobDir);
      } else {
        console.error(`Job directory does not exist: ${jobDir}`);
      }
    } catch (err) {
      console.error(`Error listing job directory:`, err);
    }

    throw error(404, 'File not found');
  }

  try {
    // Read the file
    const fileBuffer = fs.readFileSync(filePath);

    // Return the file as a response
    return new Response(fileBuffer, {
      headers: {
        'Content-Type': 'application/vnd.android.package-archive',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': fileBuffer.length.toString(),
      },
    });
  } catch (err) {
    console.error('Error reading APK file:', err);
    throw error(500, 'Error reading APK file');
  }
}
