import { getSession, verifySession } from '$lib/server/session';
import { verifyCsrfToken } from '$lib/server/csrf';
import { env } from '$env/dynamic/private';
import { ApkRepackerService } from '$lib/server/ApkRepackerService';

/** @type {import('./$types').PageServerLoad} */
export async function load() {
  // Clean up completed jobs (older than 1 hour) when the page loads
  try {
    const completedJobsRemoved = ApkRepackerService.cleanupCompletedJobs();
    const oldFilesRemoved = ApkRepackerService.cleanupTempFiles();
  } catch (error) {
    console.error('Error cleaning up temporary files:', error);
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  // Repack APK action
  repackApk: async ({ request, cookies }) => {
    // Clean up completed jobs before starting a new one
    try {
      const completedJobsRemoved = ApkRepackerService.cleanupCompletedJobs();
    } catch (error) {
      console.error('Error cleaning up completed jobs:', error);
    }

    const data = await request.formData();
    const csrfToken = data.get('csrfToken');
    const config = data.get('config');
    const apkFile = data.get('apkFile');
    const assetFilename = data.get('assetFilename') || 'vpn.conf';
    const componentId = data.get('componentId'); // Get the component ID for creating a unique job ID

    // Verify the session
    const signedSession = cookies.get('admin_session');
    /** @type {string|null} */
    const sessionId = verifySession(signedSession);
    const session = sessionId ? getSession(sessionId) : null;

    // If not authenticated or CSRF token is invalid, return error
    if (env.NODE_ENV !== 'development' && !session) {
      return { success: false, error: 'Authentication required' };
    }

    // Verify CSRF token
    if (env.NODE_ENV !== 'development' && !sessionId) {
      return { success: false, error: 'Invalid session' };
    }

    if (csrfToken) {
      const tokenStr =
        typeof csrfToken === 'string' ? csrfToken : String(csrfToken);
      if (env.NODE_ENV !== 'development' && !verifyCsrfToken(tokenStr, sessionId)) {
        return { success: false, error: 'Invalid request token' };
      }
    } else {
      return { success: false, error: 'CSRF token required' };
    }

    // Validate inputs
    if (!apkFile) {
      return { success: false, error: 'APK file is required' };
    }

    // Ensure we have a File object
    if (
      !(apkFile instanceof File) &&
      !(apkFile.constructor && apkFile.constructor.name === 'File')
    ) {
      return {
        success: false,
        error: 'Invalid file upload',
        details: `Expected File object, got ${typeof apkFile}`,
      };
    }

    // From this point, we know apkFile is a File object
    const file = /** @type {File} */ (apkFile);

    // Check if the file has content
    if (file.size === 0) {
      return { success: false, error: 'The uploaded APK file is empty' };
    }

    // Check if the file has a valid APK extension
    const fileName = file.name.toLowerCase();
    if (!fileName.endsWith('.apk')) {
      return {
        success: false,
        error: 'The uploaded file must be an APK file (.apk extension)',
      };
    }

    // Check if the file size is reasonable (at least 1MB for a valid APK)
    if (file.size < 1024 * 1024) {
      return {
        success: false,
        error: 'The uploaded APK file is too small',
        details: `File size: ${(file.size / 1024).toFixed(2)} KB. A valid APK file should be at least 1MB.`,
      };
    }

    if (!config) {
      return { success: false, error: 'Configuration is required' };
    }

    // Use the APK Repacker Service to create a new repacking job
    let configStr = typeof config === 'string' ? config : String(config);
    const assetFilenameStr =
      typeof assetFilename === 'string' ? assetFilename : String(assetFilename);

    // Determine template type from uploaded APK filename and normalize caller config if needed
    try {
      let templateType = null;
      if (file && (file.name || (file.constructor && file.constructor.name === 'File'))) {
        const fileName = typeof file.name === 'string' ? file.name : '';
        if (fileName) {
          templateType = ApkRepackerService.getTemplateType(fileName);
        }
      }
      if (!templateType) {
        // Fallback: infer from asset filename or known caller asset name
        templateType = ApkRepackerService.getTemplateType(assetFilenameStr);
      }
      if (
        templateType === 'CALLER' ||
        assetFilenameStr.toLowerCase() === ApkRepackerService.APK_CONFIG_TEMPLATES.CALLER.filename.toLowerCase()
      ) {
        configStr = ApkRepackerService.normalizeCallerConfig(configStr);
      }
    } catch {}

    // Create a custom job ID using the component ID if available
    let customJobId = undefined; // Use undefined instead of null to match the expected type
    if (componentId) {
      // Use the component ID as part of the job ID to ensure uniqueness
      customJobId = `${Date.now()}-${componentId}`;
    }

    const result = await ApkRepackerService.createRepackingJob(
      file,
      configStr,
      assetFilenameStr,
      customJobId
    );

    // Return the result directly
    return result;
  },
};
