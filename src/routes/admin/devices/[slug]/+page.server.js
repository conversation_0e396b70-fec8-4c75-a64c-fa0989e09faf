import { error, redirect } from '@sveltejs/kit';
import { getDeviceRepository, getTeamRepository } from '$lib/server/database/DatabaseFactory.js';
import { QueryOptions } from '$lib/server/database/interfaces/IDatabase.js';
import { extractSipIdFromPhoneConf } from '$lib/server/apkTemplates.js';

export const load = async ({ params }) => {
  const id = params.slug;
  const deviceRepository = getDeviceRepository();
  const teamRepository = getTeamRepository();

  // Fetch device data
  const result = await deviceRepository.findWithTeamInfo(new QueryOptions({
    where: { internal_id: id }
  }));

  if (!result.success || !result.data || result.data.length === 0) {
    console.error('Error fetching device:', result.error);
    throw error(404, 'Device not found');
  }

  // Fetch all teams for the dropdown
  const teamsResult = await teamRepository.findMany(new QueryOptions({
    select: ['id', 'internal_id'],
    orderBy: { id: 'asc' }
  }));

  if (!teamsResult.success) {
    console.error('Error fetching teams:', teamsResult.error);
    // Don't fail completely, just return empty teams array
  }

  // Convert Team instances to plain objects with only the needed properties
  const teams = (teamsResult.data || []).map(team => ({
    id: team.id,
    internal_id: team.internal_id
  }));

  // Helper function to safely convert dates to ISO strings
  const safeToISOString = (date) => {
    if (!date) return null;
    if (typeof date === 'string') return date;
    if (date instanceof Date) return date.toISOString();
    return new Date(date).toISOString();
  };

  // Convert device to plain object for serialization
  const device = result.data[0];
  const safeDevice = {
    internal_id: device.internal_id || '',
    team_id: device.team_id || '',
    team_internal_id: device.team_internal_id || '',
    ip: device.ip || '',
    nickname: device.nickname || '',
    role: device.role || '',
    lang: device.lang || '',
    created_at: safeToISOString(device.created_at) || new Date().toISOString(),
    last_auth_at: safeToISOString(device.last_auth_at),
    vpn_conf: device.vpn_conf || '',
    msg_conf: device.msg_conf || '',
    phone_conf: device.phone_conf || '',
    fcm_token: device.fcm_token || null,
    sip_id: device.sip_id || null,
    // Include team info if available
    teams: device.teams ? {
      id: device.teams.id || '',
      balance: device.teams.balance || 0
    } : null
  };

  return { device: safeDevice, teams };
};

export const actions = {
  delete: async ({ params }) => {
    const id = params.slug;
    const deviceRepository = getDeviceRepository();

    const result = await deviceRepository.deleteById(id);

    if (!result.success) {
      throw error(500, result.error?.message || 'Failed to delete device');
    }

    throw redirect(303, '/admin/devices');
  },

  update: async ({ request, params }) => {
    const currentId = params.slug;
    const formData = await request.formData();
    const deviceRepository = getDeviceRepository();
    const teamRepository = getTeamRepository();

    // Define the shape of our update fields with proper types
    /** @type {Record<string, string | null>} */
    const phoneConf = formData.get('phone_conf')?.toString() ?? '';

    // Safely extract SIP ID from phone configuration
    let sipId = null;
    try {
      sipId = phoneConf ? extractSipIdFromPhoneConf(phoneConf) : null;
    } catch (error) {
      console.warn('Failed to extract SIP ID from phone configuration:', error);
      sipId = null;
    }

    const updateFields = {
      team_id: formData.get('team_id')?.toString() ?? '',
      ip: formData.get('ip')?.toString() ?? '',
      nickname: formData.get('nickname')?.toString() ?? '',
      role: formData.get('role')?.toString() ?? '',
      lang: formData.get('lang')?.toString() ?? '',
      vpn_conf: formData.get('vpn_conf')?.toString() ?? '',
      msg_conf: formData.get('msg_conf')?.toString() ?? '',
      phone_conf: phoneConf,
      sip_id: sipId,
    };

    // Remove empty strings and convert to null
    for (const key in updateFields) {
      if (updateFields[key] === '') {
        updateFields[key] = null;
      }
    }

    // If no fields to update, return early
    if (Object.values(updateFields).every((val) => val === null)) {
      return { success: true };
    }

    // If team_id is provided, verify it exists and get internal_id
    if (updateFields.team_id) {
      const teamResult = await teamRepository.findByTeamId(updateFields.team_id, new QueryOptions({
        select: ['internal_id']
      }));

      if (!teamResult.success || !teamResult.data) {
        return {
          success: false,
          error: 'team not found',
        };
      }

      // Add team_internal_id to the update fields
      updateFields.team_internal_id = teamResult.data.internal_id;
    } else {
      // If team_id is null/empty, remove it from update to preserve existing team relationship
      // This prevents breaking NOT NULL constraints on team_internal_id and team_id
      delete updateFields.team_id;
      delete updateFields.team_internal_id;
    }

    try {
      // Update the device
      const updateResult = await deviceRepository.updateById(currentId, updateFields);

      if (!updateResult.success) {
        throw new Error(updateResult.error?.message || 'Failed to update device');
      }

      if (!updateResult.data) {
        throw new Error('No data returned from update');
      }

      // Redirect to devices list after successful update
      throw redirect(303, '/admin/devices');
    } catch (/** @type {any} */ err) {
      if (err.status === 303) throw err; // Re-throw redirect
      console.error('Error updating device:', err);
      return {
        success: false,
        error: err instanceof Error ? err.message : 'Failed to update device',
      };
    }
  },
};
