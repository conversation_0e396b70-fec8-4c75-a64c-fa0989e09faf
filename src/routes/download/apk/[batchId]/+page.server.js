import { error } from '@sveltejs/kit';
import fs from 'fs';
import path from 'path';
import { ApkRepackerService } from '$lib/server/ApkRepackerService';

/**
 * Load function for the public APK download page
 * This page allows downloading APKs without authentication using a batch ID
 */
export async function load({ params }) {
  const { batchId } = params;

  // Validate batch ID format (should start with "batch-" followed by timestamp)
  if (!batchId || !batchId.startsWith('batch-')) {
    throw error(404, 'Invalid batch ID');
  }

  try {
    // Extract timestamp from batch ID
    const timestampMatch = batchId.match(/^batch-(\d+)$/);
    if (!timestampMatch) {
      throw error(404, 'Invalid batch ID format');
    }

    const timestamp = timestampMatch[1];
    
    // Look for directories that contain this batch ID
    const tmpDir = ApkRepackerService.TMP_DIR;
    
    if (!fs.existsSync(tmpDir)) {
      throw error(404, 'No APK files found');
    }

    const dirs = fs.readdirSync(tmpDir);
    
    // Find directories that match this batch
    const batchDirs = dirs.filter(dir => dir.includes(batchId));


    if (batchDirs.length === 0) {
      throw error(404, `No APK files found for batch ${batchId}`);
    }

    // Collect all APK files from batch directories
    const apkFiles = [];
    
    for (const dir of batchDirs) {
      const dirPath = path.join(tmpDir, dir);
      
      try {
        const files = fs.readdirSync(dirPath);

        // Only include final repacked APKs, exclude input.apk and temp files
        const apkFilesInDir = files.filter(file =>
          file.toLowerCase().endsWith('.apk') &&
          file.toLowerCase() !== 'input.apk' // Exclude input.apk, include all output APKs
        );



        for (const apkFile of apkFilesInDir) {
          try {
            const filePath = path.join(dirPath, apkFile);
            const stats = fs.statSync(filePath);


            // Extract device info from directory name if possible
            const deviceMatch = dir.match(/device-([^-]+)/);
            const deviceId = deviceMatch ? deviceMatch[1] : 'unknown';

            // Extract service type from directory name
            let serviceName = 'Unknown APK';
            let serviceType = 'other';

            // Extract service type from directory name (e.g., "batch-123-device-abc-chat-456" -> "chat")
            const serviceMatch = dir.match(/-([^-]+)-\d+$/);
            if (serviceMatch) {
              serviceType = serviceMatch[1].toLowerCase();

              // Map service types to display names
              switch (serviceType) {
                case 'chat':
                case 'messenger':
                  serviceName = 'Messenger APK';
                  break;
                case 'vpn':
                  serviceName = 'VPN APK';
                  break;
                case 'phantom':
                  serviceName = 'Phantom APK';
                  break;
                case 'caller':
                  serviceName = 'Caller APK';
                  break;
                default:
                  serviceName = `${serviceType.charAt(0).toUpperCase() + serviceType.slice(1)} APK`;
              }
            }

            // Generate a user-friendly filename
            const friendlyFilename = `${serviceType}.apk`;

            // Use the most reliable date available - prefer mtime (modification time) over birthtime
            // as birthtime can be unreliable on some file systems
            const createdDate = stats.birthtime && stats.birthtime.getTime() > 0 
              ? stats.birthtime 
              : stats.mtime;

            const apkFileInfo = {
              filename: apkFile, // Keep original for download URL
              friendlyFilename, // User-friendly display name
              deviceId,
              serviceName,
              serviceType,
              size: stats.size,
              created: createdDate,
              downloadUrl: `/download/apk/${batchId}/${dir}/${apkFile}`,
              // Add a WebView-friendly URL with friendly filename in the path
              webViewDownloadUrl: `/download/apk/${batchId}/${dir}/${friendlyFilename}?original=${encodeURIComponent(apkFile)}`
            };

            apkFiles.push(apkFileInfo);
          } catch (fileErr) {
            console.error(`[APK Download] Error processing APK file:`, fileErr);
          }
        }
      } catch (err) {
        console.error(`Error reading batch directory:`, err);
      }
    }


    if (apkFiles.length === 0) {
      throw error(404, `No APK files found in batch ${batchId}. Found ${batchDirs.length} directories but no valid APK files.`);
    }

    // Sort by creation time (newest first)
    apkFiles.sort((a, b) => b.created.getTime() - a.created.getTime());

    return {
      batchId,
      apkFiles,
      totalFiles: apkFiles.length,
      batchCreated: new Date(parseInt(timestamp))
    };
  } catch (err) {
    console.error('Error loading batch APK files:', err);
    if (err.status) {
      throw err; // Re-throw SvelteKit errors
    }
    throw error(500, 'Error loading APK files');
  }
}
