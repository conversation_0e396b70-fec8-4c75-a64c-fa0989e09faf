# Call Charge Processing Implementation

## Overview

This implementation adds a new cron job that runs every 10 minutes to process unprocessed call charges from the `call_charges` table. The system is designed to be atomic and race-condition safe.

## Key Features

- **Race-Condition Safe Processing**: Uses application-level techniques to prevent double-processing
- **Automatic Team Balance Updates**: Deducts call charges from team balances
- **Transaction Logging**: Creates transaction records for all call charges
- **Multi-language Notifications**: Sends localized notifications to teams
- **Error Handling**: Comprehensive error handling and logging
- **Batch Processing**: Processes charges in configurable batches

## Files Created/Modified

### New Files Created:

1. **`src/lib/server/database/entities/CallCharge.js`**
   - Entity class for call charges
   - Validation and data transformation methods

2. **`src/lib/server/database/interfaces/ICallChargeRepository.js`**
   - Interface defining call charge repository operations
   - Includes atomic processing methods

3. **`src/lib/server/database/repositories/supabase/SupabaseCallChargeRepository.js`**
   - Supabase implementation of call charge repository
   - Implements atomic batch processing using database functions

4. **`src/lib/server/cron/callChargeJobs.js`**
   - Main call charge processing logic
   - Maps SIP users to teams and processes charges

### Files Modified:

1. **`src/lib/server/database/DatabaseFactory.js`**
   - Added CallCharge repository initialization
   - Added convenience function `getCallChargeRepository()`

2. **`src/lib/server/cron/index.js`**
   - Added new cron job running every 10 minutes
   - Updated control functions to include call charge processing

3. **`src/lib/server/cron/utils/notificationTemplates.js`**
   - Added call charge notification templates
   - Added multi-language support (EN/RU/UA)
   - Added duration formatting utility

## Race Condition Prevention

Since we're using Supabase only as storage (no custom database functions), the implementation uses sequential processing with delays to prevent race conditions:

### Sequential Processing Strategy

1. **Ordered Selection**: Selects unprocessed charges in chronological order (oldest first)
2. **Sequential Processing**: Processes charges one by one in order
3. **Configurable Delays**: Adds configurable delays between processing each charge (via `PROCESSING_DELAY_MS`)
4. **Conditional Updates**: Only updates records that are still `processed = false`
5. **Conflict Detection**: Handles cases where records are already processed by other instances
6. **Unique Processing IDs**: Each batch gets a unique ID for tracking and debugging

### Safety Mechanisms

- **Predictable Timing**: Sequential processing with consistent delays
- **Double-Check Updates**: Each charge is updated only if it's still unprocessed
- **Error Isolation**: If one charge fails, others continue processing with delays
- **Comprehensive Logging**: Detailed logs with processing IDs for debugging
- **Graceful Degradation**: Handles concurrent processing gracefully

## How It Works

### 1. Cron Job Execution
- Runs at configurable intervals (default: every 3 minutes `*/3 * * * *`)
- Configurable via `CALL_CHARGE_CRON_PATTERN` environment variable
- Calls `processCallCharges()` function

### 2. Sequential Processing
- Uses `atomicProcessBatch()` to safely get and mark charges as processed
- Prevents race conditions using sequential processing with configurable delays
- Each charge is processed atomically with conditional updates

### 3. Team Mapping
- Maps `call_charges.sip_user` → `devices.sip_id` → `devices.team_id`
- Groups charges by SIP user for batch processing
- Uses proper device-to-team relationship

### 4. Balance Updates
- Checks team balance before processing
- Updates team balance atomically
- Creates transaction records with `call_charge: true`

### 5. Notifications
- Sends localized notifications to teams
- Includes charge amount, duration, and new balance

## Configuration

The system uses existing billing configuration plus new call charge settings:
- `BILLING_BATCH_SIZE`: Number of charges to process per batch (default: 50)
- `BILLING_NOTIFICATION_ENABLED`: Enable/disable notifications
- `BILLING_TIMEZONE`: Timezone for cron scheduling
- `PROCESSING_DELAY_MS`: Delay between processing each charge in milliseconds (default: 5000)
- `CALL_CHARGE_CRON_PATTERN`: Cron pattern for call charge processing frequency (default: '*/3 * * * *')

### Example Configurations

```bash
# Every 5 minutes (for high call volume)
CALL_CHARGE_CRON_PATTERN="*/5 * * * *"

# Every 3 minutes (default)
CALL_CHARGE_CRON_PATTERN="*/3 * * * *"

# Every 30 minutes (for low call volume)
CALL_CHARGE_CRON_PATTERN="*/30 * * * *"

# Every hour at minute 0
CALL_CHARGE_CRON_PATTERN="0 * * * *"

# Every 15 minutes during business hours (9 AM - 5 PM)
CALL_CHARGE_CRON_PATTERN="*/15 9-17 * * *"

# Fast processing with short delays (for testing)
CALL_CHARGE_CRON_PATTERN="*/2 * * * *"
PROCESSING_DELAY_MS=1000
```

## Error Handling

- **Insufficient Balance**: Logs warning, skips charge, continues processing
- **Team Not Found**: Logs warning, skips charge, continues processing
- **Database Errors**: Logs error, continues with next batch
- **Notification Failures**: Logs error, doesn't fail the operation

## Monitoring

The cron job provides detailed logging:
- Number of charges processed
- Number of teams charged
- Processing duration
- Error details

Example log output:
```
[Call Charges] Processed 25 charges, charged 8 teams in 1250ms
[Call Charges] Encountered 2 errors: [...]
```

## Testing

You can manually trigger the job using the cron controls:
```javascript
const controls = getBillingCronControls(app);
await controls.triggerJob('call-charge-processing');
```

## Important Notes

1. **SIP User Mapping**: The implementation uses the proper mapping: `call_charges.sip_user` → `devices.sip_id` → `devices.team_id`. This ensures accurate team identification for billing.

2. **Race Condition Safety**: The application-level sequential processing ensures no charge is processed twice, even with multiple instances running. Uses ordered selection with configurable delays between charges.

3. **Transaction Marking**: All call charge transactions are marked with `call_charge: true` for easy identification.

4. **Notification Language**: The system detects team language from device settings and sends localized notifications.

5. **Balance Validation**: Teams with insufficient balance will have their charges skipped and logged as errors.

6. **Performance**: Sequential processing with configurable delays is slower but much safer and predictable. With default settings (5-second delays), each batch of 50 charges takes ~4 minutes to process. Adjust `PROCESSING_DELAY_MS` and batch sizes based on your call volume and acceptable processing delays.

## Next Steps

1. ~~Create the database function in Supabase~~ (Not needed - using application-level processing)
2. Verify the SIP user to team mapping logic
3. Test with sample call charge data
4. Monitor the cron job logs for proper operation
5. Adjust batch size and frequency as needed based on call volume
6. Consider optimizing batch processing if performance becomes an issue