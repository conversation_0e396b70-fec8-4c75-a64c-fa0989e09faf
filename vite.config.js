import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';
import { resolve } from 'path';
import fs from 'fs';
import path from 'path';

export default defineConfig({
  plugins: [
    sveltekit(),
    {
      name: 'copy-repack-apk',
      apply: 'build',
      enforce: 'post',
      generateBundle() {
        try {
          // Get all files in the _repack_apk directory
          const repackDir = resolve('./_repack_apk');

          // Check if directory exists
          if (!fs.existsSync(repackDir)) {
            console.warn(
              'Warning: _repack_apk directory not found, skipping copy'
            );
            return;
          }

          const files = fs.readdirSync(repackDir);

          // Copy each file to the build output
          files.forEach((file) => {
            const filePath = path.join(repackDir, file);
            const stats = fs.statSync(filePath);

            // Skip directories and .DS_Store files
            if (stats.isDirectory() || file === '.DS_Store') {
              return;
            }

            // Read the file and emit it as an asset
            // Copy to a single, predictable location within the server output
            this.emitFile({
              type: 'asset',
              fileName: `server/_repack_apk/${file}`,
              source: fs.readFileSync(filePath),
            });
          });
        } catch (error) {
          console.error('Error in copy-repack-apk plugin:', error);
        }
      },
    },
    {
      name: 'copy-rates-file',
      apply: 'build',
      enforce: 'post',
      generateBundle() {
        try {
          // Copy rates.txt file to the build output
          const ratesFile = resolve('./rates.txt');
          
          // Check if rates.txt exists
          if (!fs.existsSync(ratesFile)) {
            console.warn(
              'Warning: rates.txt file not found, skipping copy'
            );
            return;
          }

          // Read the file and emit it as an asset
          this.emitFile({
            type: 'asset',
            fileName: '../rates.txt',
            source: fs.readFileSync(ratesFile),
          });

        } catch (error) {
          console.error('Error in copy-rates-file plugin:', error);
        }
      },
    },
  ],
  build: {
    sourcemap: false,
    minify: true,
  }
});
