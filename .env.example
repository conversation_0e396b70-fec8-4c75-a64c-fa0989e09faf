# =============================================================================
# PHANTOM WEB ADMIN CONFIGURATION
# =============================================================================

# -----------------------------------------------------------------------------
# Database Configuration
# -----------------------------------------------------------------------------
# Database provider selection - determines which database implementation to use
# Options: supabase, postgresql, mysql, sqlite
DATABASE_PROVIDER=supabase

# Supabase Configuration (when DATABASE_PROVIDER=supabase)
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key

# PostgreSQL Configuration (when DATABASE_PROVIDER=postgresql)
# DATABASE_URL=postgresql://user:password@localhost:5432/phantom
# DATABASE_HOST=localhost
# DATABASE_PORT=5432
# DATABASE_NAME=phantom
# DATABASE_USER=your_db_user
# DATABASE_PASSWORD=your_db_password

# MySQL Configuration (when DATABASE_PROVIDER=mysql)
# DATABASE_URL=mysql://user:password@localhost:3306/phantom
# DATABASE_HOST=localhost
# DATABASE_PORT=3306
# DATABASE_NAME=phantom
# DATABASE_USER=your_db_user
# DATABASE_PASSWORD=your_db_password

# SQLite Configuration (when DATABASE_PROVIDER=sqlite)
# DATABASE_PATH=./data/phantom.db

# -----------------------------------------------------------------------------
# Authentication & Security
# -----------------------------------------------------------------------------
# Admin dashboard authentication
ADMIN_PASSWORD=your_admin_password

# Session and CSRF protection
CSRF_SECRET=your_csrf_secret
SESSION_SECRET=your_session_secret

# API authentication
API_KEY=your_api_key
WEBVIEW_AUTH_TOKEN=your_secret_token_here

# -----------------------------------------------------------------------------
# External Service Integrations
# -----------------------------------------------------------------------------
# CoinMarketCap API for cryptocurrency data
CMC_API_KEY=your_cmc_api_key

# WestWallet payment processing
WESTWALLET_API_KEY=your_westwallet_api_key
WESTWALLET_SECRET_KEY=your_westwallet_secret_key

# Firebase Cloud Messaging for push notifications
GOOGLE_APPLICATION_CREDENTIALS=./phantom-app-6cd84-firebase-adminsdk-fbsvc-bb2d3c117d.json

# -----------------------------------------------------------------------------
# Development & Debugging
# -----------------------------------------------------------------------------
# Set to 'development' for detailed logging and debugging
# NODE_ENV=development

# Enable database query logging (useful for development)
# DATABASE_LOGGING=true

# -----------------------------------------------------------------------------
# Billing Configuration
# -----------------------------------------------------------------------------
# Billing system settings for automated charge processing
BILLING_CYCLE_DAYS=30
BILLING_NOTIFICATION_ENABLED=true
BILLING_TIMEZONE=Europe/Kyiv
BILLING_RETRY_ATTEMPTS=3
BILLING_BATCH_SIZE=50