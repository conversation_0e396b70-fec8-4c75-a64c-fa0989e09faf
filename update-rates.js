#!/usr/bin/env node

/**
 * Update rates.txt file with current exchange rates from CoinMarketCap API
 * 
 * Usage: node update-rates.js
 * 
 * This script fetches current cryptocurrency exchange rates from CoinMarketCap
 * and updates the rates.txt file with the latest values.
 */

import fs from 'fs';
import path from 'path';
import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// CoinMarketCap API configuration
const CMC_API_KEY = process.env.CMC_API_KEY;
const CMC_BASE_URL = 'https://pro-api.coinmarketcap.com/v1';

// Default rates file path
const RATES_FILE_PATH = path.resolve(process.cwd(), 'rates.txt');

// Supported currencies and their CoinMarketCap symbols
const CURRENCY_MAPPING = {
  USDTTRC: 'USDT', // USDT TRC-20 maps to USDT symbol
  USDT: 'USDT',
  BTC: 'BTC',
  ETH: 'ETH',
  TRX: 'TRX',
  SOL: 'SOL',
  NOT: 'NOT', // Notcoin
  XMR: 'XMR',
  XRP: 'XRP',
  DOGE: 'DOGE',
};

// Fallback rates (used when API fails)
const FALLBACK_RATES = {
  USDTTRC: 1.0,
  USDT: 1.0,
  BTC: 102000,
  ETH: 2270,
  TRX: 0.08,
  SOL: 173.5,
  NOT: 0.01,
  XMR: 150,
  XRP: 2.04,
  DOGE: 0.08,
};

/**
 * Fetch current exchange rates from CoinMarketCap API
 * @returns {Promise<Object>} Object with currency rates or null if failed
 */
async function fetchCurrentRates() {
  try {
    if (!CMC_API_KEY) {
      console.error('❌ CMC_API_KEY environment variable not found');
      return null;
    }


    // Get all supported symbols for batch request
    const symbols = Object.values(CURRENCY_MAPPING)
      .filter((symbol, index, arr) => arr.indexOf(symbol) === index) // Remove duplicates
      .join(',');


    // Make batch API request to CoinMarketCap
    const response = await axios.get(
      `${CMC_BASE_URL}/cryptocurrency/quotes/latest`,
      {
        headers: {
          'X-CMC_PRO_API_KEY': CMC_API_KEY,
          Accept: 'application/json',
        },
        params: {
          symbol: symbols,
          convert: 'USD',
        },
        timeout: 30000, // 30 second timeout for batch request
      }
    );

    if (!response.data || !response.data.data) {
      console.error('❌ Invalid response format from CoinMarketCap API');
      return null;
    }

    const rates = {};
    let updateCount = 0;

    // Map API response to our currency codes
    for (const [currency, cmcSymbol] of Object.entries(CURRENCY_MAPPING)) {
      if (response.data.data[cmcSymbol]) {
        const price = response.data.data[cmcSymbol].quote.USD.price;
        rates[currency] = price;
        updateCount++;
      } else {
        console.warn(`⚠️  No data found for ${currency} (${cmcSymbol})`);
        // Use fallback rate
        rates[currency] = FALLBACK_RATES[currency];
      }
    }

    return rates;

  } catch (error) {
    console.error('❌ Error fetching rates from CoinMarketCap API:', error.message);
    
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Data: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    
    return null;
  }
}

/**
 * Read existing rates from rates.txt file
 * @returns {Object} Existing rates or empty object
 */
function readExistingRates() {
  try {
    if (!fs.existsSync(RATES_FILE_PATH)) {
      return {};
    }

    const content = fs.readFileSync(RATES_FILE_PATH, 'utf8');
    const rates = {};
    
    const lines = content.split('\n');
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [currency, rate] = trimmedLine.split('=');
        if (currency && rate) {
          const parsedRate = parseFloat(rate);
          if (!isNaN(parsedRate)) {
            rates[currency.trim()] = parsedRate;
          }
        }
      }
    }
    
    return rates;
  } catch (error) {
    console.error('❌ Error reading existing rates file:', error.message);
    return {};
  }
}

/**
 * Write rates to rates.txt file
 * @param {Object} rates - Rates object to write
 * @param {Object} existingRates - Existing rates for comparison
 */
function writeRatesToFile(rates, existingRates = {}) {
  try {
    const timestamp = new Date().toISOString();
    let content = `# Exchange rates updated on ${timestamp}\n`;
    content += `# Generated by update-rates.js script\n`;
    content += `# Format: CURRENCY=RATE_IN_USD\n\n`;

    // Sort currencies for consistent output
    const sortedCurrencies = Object.keys(rates).sort();
    
    let changedCount = 0;
    
    for (const currency of sortedCurrencies) {
      const rate = rates[currency];
      const oldRate = existingRates[currency];
      
      content += `${currency}=${rate}\n`;
      
      if (oldRate !== undefined && Math.abs(oldRate - rate) > 0.000001) {
        const change = ((rate - oldRate) / oldRate * 100).toFixed(2);
        changedCount++;
      }
    }

    fs.writeFileSync(RATES_FILE_PATH, content, 'utf8');
    
    if (changedCount > 0) {
    } else {
    }
    
  } catch (error) {
    console.error('❌ Error writing rates to file:', error.message);
    throw error;
  }
}

/**
 * Main function to update rates
 */
async function updateRates() {

  try {
    // Read existing rates for comparison
    const existingRates = readExistingRates();

    // Fetch current rates from API
    const currentRates = await fetchCurrentRates();

    if (!currentRates) {
      writeRatesToFile(FALLBACK_RATES, existingRates);
      return;
    }

    // Write updated rates to file
    writeRatesToFile(currentRates, existingRates);
    
    
  } catch (error) {
    console.error('\n❌ Failed to update rates:', error.message);
    process.exit(1);
  }
}

/**
 * Show usage information
 */
function showUsage() {
📋 Usage: node update-rates.js

This script updates the rates.txt file with current cryptocurrency exchange rates
from the CoinMarketCap API.

🔧 Setup:
1. Set your CoinMarketCap API key in the .env file:
   CMC_API_KEY=your_api_key_here

2. Run the script:
   node update-rates.js

📁 Output:
- Updates rates.txt in the current directory
- Shows rate changes and statistics
- Falls back to hardcoded rates if API fails

🌐 Supported currencies:
${Object.keys(CURRENCY_MAPPING).join(', ')}
`);
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  showUsage();
  process.exit(0);
}

// Run the update
updateRates();