# Phantom Web Admin

A comprehensive admin dashboard and API system for managing VPN teams, devices, wallets, and transactions with enterprise-grade analytics and database abstraction.

## Project Components

* **Admin Dashboard** - Enterprise-grade statistics dashboard with comprehensive analytics
* **Database Abstraction Layer** - Repository pattern enabling database provider switching
* **API Endpoints** - RESTful APIs for mobile app and VPN node integration
* **Nginx Configuration** - Production-ready web server setup
* **Installation Scripts** - Automated deployment tools
    * **VPN Node Setup** - Automated installation script for WireGuard VPN nodes
    * **APK Repackaging Dependencies** - Tools for repackaging APK files

### Supported Integrations

* **[Phantom Android App](https://github.com/3geeze/phantom-android-app)** - Mobile billing application
  * FCM token registration
  * WestWallet IPN processing
* **[Phantom VPN Node](https://github.com/3geeze/phantom-vpn-node)** - VPN server management

## API Endpoints

### VPN Management
- `POST /api/vpn` - Update client status from VPN nodes
- `GET /api/clients` - List all VPN clients with status information
- `POST /api/client/{id}/status` - Enable or disable a specific VPN client
- `POST /api/client/{id}/acknowledge` - Acknowledge status update from VPN node
- `GET /api/report` - Generate comprehensive client status report
- `POST /api/client/{id}/trust-node` - Resolve conflicts by trusting node state

### Notifications & Payments
- `POST /api/notifications/token` - Update FCM token for Android device notifications
- `POST /api/notifications/ipn` - Handle payment notifications from WestWallet
- `GET /api/notifications/ipn` - Get IPN endpoint status and configuration

### Utilities
- `GET /api/ip` - Get client IP address

## Script Installations

### Install on VPN node running WireGuard

```shell
curl -sL https://my-phantomos.com/install/phantom-vpn-node.sh | bash
```

### Install APK repacking dependencies

```shell
curl -sL https://my-phantomos.com/install/install-repack-apk-deps.sh | bash
```

### Repack an APK example

```shell
./repack_apk.sh vpn.apk -f vpn.conf -n test.apk -k key.keystore -s signer.jar -a key0 -p 123321
```

## Tech Stack

*   **Framework:** [SvelteKit](https://kit.svelte.dev/)
*   **Bundler:** [Vite](https://vitejs.dev/)
*   **Runtime / Package Manager:** [Bun](https://bun.sh/)
*   **Current DB Provider:** [Supabase](https://supabase.io/)
*   **Future DB Providers:** SQLite, PostgreSQL (via abstraction layer)

## Getting Started

### Prerequisites

*   [Node.js](https://nodejs.org/) (v18 or higher)
*   [Bun](https://bun.sh/)

### Installation

1.  Clone the repository:
    ```bash
    git clone <repository-url>
    ```
2.  Navigate to the project directory:
    ```bash
    cd phantom-web-admin
    ```
3.  Install the dependencies:
    ```bash
    bun install
    ```
4.  Create a `.env` file by copying the example:
    ```bash
    cp .env.example .env
    ```
5.  Update the `.env` file with your credentials and database provider.

### Database Configuration

The application supports multiple database providers through environment configuration:

```bash
# Database provider selection
DATABASE_PROVIDER=supabase  # Options: supabase, postgresql, mysql, sqlite

# Supabase configuration (when using Supabase provider)
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key
```

### Running in Development Mode

To start the development server, run:

```bash
bun run dev
```

The application will be available at `http://localhost:5173`.

## Available Scripts

*   `bun run dev`: Starts the development server.
*   `bun run build`: Creates a production-ready build of the application.
*   `bun run preview`: Starts a local server to preview the production build.

## Building for Production

To build the application for production, run:

```bash
bun run build
```

The build artifacts will be stored in the `build/` directory.

## Database

### Database Abstraction Layer

The application implements a comprehensive repository pattern that provides:

- **Database Independence** - Switch between providers without code changes
- **Type Safety** - Entity models with business logic validation
- **Comprehensive Analytics** - Advanced statistics and reporting capabilities
- **Error Handling** - Standardized error objects and validation

### Repository Pattern Structure

```
src/lib/server/database/
├── interfaces/          # Repository and database interfaces
├── entities/           # Entity models with validation
├── repositories/       # Provider-specific implementations
└── DatabaseFactory.js # Dependency injection container
```

### Supported Entities

- **Teams** - VPN team management with balance tracking
- **Devices** - Device registration and activity monitoring
- **Wallets** - Multi-currency wallet management
- **Transactions** - Payment processing and analytics

### Switching Database Providers

The repository pattern enables seamless switching between database providers:

1. **Update Environment Variable:**
   ```bash
   DATABASE_PROVIDER=postgresql  # Switch from supabase to postgresql
   ```

2. **Add Provider Configuration:**
   ```bash
   # PostgreSQL configuration
   DATABASE_URL=postgresql://user:password@localhost:5432/phantom
   ```

3. **Restart Application** - No code changes required!

### Supported Providers

- ✅ **Supabase** - Fully implemented and production-ready
- ⏳ **SQLite** - Architecture ready, implementation TBD
- ⏳ **PostgreSQL** - Architecture ready, implementation TBD